<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UI Dashboard</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=ZCOOL+KuaiLe&display=swap');

        :root {
            font-size: 16px;
            --fs-xs: .75rem;
            --fs-sm: .875rem;
            --fs-base: 1rem;
            --fs-md: 1.125rem;
            --fs-lg: 1.25rem;
            --fs-xl: 1.5rem;
            --fw-n: 400;
            --fw-m: 500;
            --fw-b: 700;
            --lh-t: 1.2;
            --lh-n: 1.5;
            --ls-w: .05em;
            --trans: all .3s ease;
            --radius: .5rem;
            --sp-xs: .5%;
            --sp-sm: 1%;
            --sp-md: 2%;
            --sp-lg: 3%;
            --sp-xl: 5%
        }

        .theme-dark {
            --c1: #000;
            --c2: #1a1a1a;
            --c3: #333;
            --c4: #555;
            --c5: #888;
            --c6: #aaa
        }

        .theme-light {
            --c1: #fff;
            --c2: #e0e0e0;
            --c3: #ccc;
            --c4: #aaa;
            --c5: #777;
            --c6: #555
        }

        .theme-brown {
            --c1: #1a120b;
            --c2: #3c2a21;
            --c3: #4e342e;
            --c4: #b4968c;
            --c5: #ebddd9;
            --c6: #ffffff
        }

        * {
            box-sizing: border-box
        }

        body {
            margin: 0;
            font: var(--fs-base)/var(--lh-n) 'ZCOOL KuaiLe', sans-serif;
            color: var(--c5)
        }

        #dashboard-container {
            position: relative;
            aspect-ratio: 15/9;
            border: 1px solid var(--c3);
            border-radius: 0 1rem 0 0;
            overflow: hidden
        }

        .Content {
            position: relative;
            z-index: 2;
            height: 100%;
            width: 100%;
            background-color: transparent;
        }

        h1,
        h2,
        h3 {
            margin: 0 0 var(--sp-sm);
            font-weight: var(--fw-b);
            line-height: var(--lh-t)
        }

        h1 {
            font-size: var(--fs-xl);
            color: var(--c6)
        }

        h2 {
            font-size: var(--fs-lg);
            color: var(--c6)
        }

        h3 {
            font-size: var(--fs-md);
            color: var(--c4)
        }

        b {
            color: var(--c4);
            margin: 0 0 var(--sp-sm);
            font-size: var(--fs-md);
            font-weight: var(--fw-m);
            display: block
        }

        p {
            margin: var(--sp-lg) var(--sp-lg) var(--sp-xl) 0;
            word-break: break-all;
            white-space: pre-line
        }

        ::-webkit-scrollbar {
            width: 8px
        }

        ::-webkit-scrollbar-track {
            background: color-mix(in srgb, var(--c1) 30%, transparent);
            border-radius: 4px
        }

        ::-webkit-scrollbar-thumb {
            background: color-mix(in srgb, var(--c5) 50%, transparent);
            border-radius: 4px;
            transition: var(--trans)
        }

        ::-webkit-scrollbar-thumb:hover {
            background: color-mix(in srgb, var(--c5) 80%, transparent)
        }

        /* 左侧选单 */
        .leftside-menu {
            display: block;
            position: absolute;
            height: 100%;
            width: 10%;
            background: linear-gradient(135deg, var(--c2), var(--c1));
            text-align: center;
            border-radius: 0 0 30% 0;
            border-right: 1px solid var(--c2);
            z-index: 4
        }

        .avatar {
            margin: 55% 0 40%;
            display: flex;
            flex-direction: column;
            align-items: center
        }

        .icon {
            width: 8vw;
            height: 8vw;
            border-radius: 50%;
            box-shadow: inset 20px 20px 60px var(--c1), inset -20px -20px 60px var(--c4);
            animation: rotate 6s linear infinite
        }

        @keyframes rotate {
            to {
                transform: rotate(360deg)
            }
        }

        .icon p {
            color: var(--c4);
            font-size: var(--fs-xl);
            margin: 12px 0 0;
            text-shadow: 0 0 3px rgba(0, 0, 0, .2)
        }

        .menu li {
            list-style: none;
            color: var(--c4);
            padding: 10%;
            text-transform: uppercase;
            font-size: var(--fs-sm);
            letter-spacing: var(--ls-w);
            cursor: pointer;
            transition: var(--trans);
            border-bottom: 1px solid var(--c3);
            position: relative;
            overflow: hidden
        }

        .menu li::before {
            content: '';
            position: absolute;
            inset: 0 auto 0 -100%;
            width: 100%;
            background: linear-gradient(90deg, transparent, color-mix(in srgb, var(--c4) 10%, transparent), transparent);
            transition: left .5s
        }

        .menu li:hover::before {
            left: 100%
        }

        .menu li:hover {
            background: linear-gradient(135deg, var(--c2), var(--c1));
            color: var(--c5);
            transform: translateX(7%);
            text-shadow: 0 0 5px rgba(0, 0, 0, .3);
            box-shadow: 0 0 10px color-mix(in srgb, var(--c5) 10%, transparent);
            border-radius: 0 0 1rem 0
        }

        .menu li.active {
            background: var(--c3) !important;
            color: var(--c5) !important;
            width: 120%;
            margin-left: -5%;
            border-radius: 0 0 1rem 0;
            border-bottom: 1px solid var(--c4);
            text-shadow: 0 0 3px rgba(0, 0, 0, .2)
        }

        .PingYongmap {
            position: absolute;
            inset: 0;
            width: 100%;
            height: 100%;
            z-index: 1
        }

        .PingYongmap iframe {
            width: 100%;
            height: 100%;
            border: 0;
            pointer-events: none
        }

        .map-ui {
            position: absolute;
            height: 100%;
            width: 13%;
            background: color-mix(in srgb, var(--c1) 50%, transparent);
            z-index: 3;
            clip-path: polygon(100% 0, 100% 93%, 85% 96%, 100% 99%, 100% 100%, 0 100%, 0 0);
            transition: var(--trans);
            cursor: pointer
        }

        .map-ui:hover {
            transform: translateX(20%)
        }

        /* 地图容器 */
        .map {
            position: absolute;
            inset: 0;
            width: 100%;
            height: 100%;
            z-index: 3;
            display: none;
            pointer-events: none;
        }

        .location-info {
            position: absolute;
            top: var(--sp-md);
            right: var(--sp-md);
            display: flex;
            flex-direction: column;
            gap: var(--sp-sm);
            z-index: 4;
            pointer-events: auto
        }

        .date-primary {
            background: linear-gradient(90deg, color-mix(in srgb, var(--c2) 60%, transparent), transparent);
            padding: var(--sp-md);
            color: var(--c6);
            font-size: var(--fs-md);
            font-weight: var(--fw-m);
            border-radius: var(--radius);
            white-space: nowrap;
            min-width: 10%;
        }

        .current-location {
            background: linear-gradient(90deg, color-mix(in srgb, var(--c3) 60%, transparent), transparent);
            padding: var(--sp-md);
            color: var(--c5);
            font-size: var(--fs-base);
            border-radius: var(--radius);
            display: flex;
            align-items: center;
            gap: var(--sp-sm)
        }

        .location-icon {
            font-size: var(--fs-md);
            color: var(--c6)
        }

        /* 整合的右侧面板 - 贴住边界 */
        .map-info-panel {
            position: absolute;
            left: 0;
            top: 0;
            width: 28%;
            height: 100%;
            background: linear-gradient(135deg, color-mix(in srgb, var(--c2) 30%, transparent), color-mix(in srgb, var(--c1) 70%, transparent));
            border-right: 1px solid var(--c3);
            border-radius: 0;
            padding: var(--sp-lg);
            display: flex;
            flex-direction: column;
            gap: var(--sp-lg);
            z-index: 4;
            overflow-y: auto;
            pointer-events: auto;
        }

        /* AP部分 */
        .action-points-section {
            background: linear-gradient(135deg, var(--c3), var(--c2));
            border-radius: var(--radius);
            padding: var(--sp-md);
            text-align: center;
            flex-shrink: 0;
        }

        .action-points {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--sp-md)
        }

        .action-points span:first-child {
            font-size: var(--fs-base);
            color: var(--c5)
        }

        .action-points span:last-child {
            font-size: var(--fs-xl);
            font-weight: var(--fw-b);
            color: var(--c6)
        }

        /* 地图描述部分 */
        .map-description {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            gap: var(--sp-md);
        }

        .map-description .title {
            color: var(--c6);
            font-size: var(--fs-md);
            font-weight: var(--fw-m);
            margin-bottom: var(--sp-sm);
            border-bottom: 1px solid var(--c3);
            padding-bottom: var(--sp-sm)
        }

        .map-description p {
            color: var(--c5);
            font-size: var(--fs-base);
            line-height: 1.6;
            margin: 0;
            white-space: pre-line
        }

        /* 建筑物部分 */
        .map-buildings {
            flex-shrink: 0;
            display: flex;
            flex-direction: column;
            gap: var(--sp-md);
            border-top: 1px solid var(--c3);
            padding-top: var(--sp-lg);
        }

        .map-buildings .sub-location {
            color: var(--c6);
            font-size: var(--fs-md);
            font-weight: var(--fw-m);
            margin-bottom: var(--sp-sm);
            border-bottom: 1px solid var(--c3);
            padding-bottom: var(--sp-sm)
        }

        .buildings-list {
            list-style-type: none;
            padding: 0;
            margin: 0
        }

        .buildings-list li {
            padding: var(--sp-sm);
            margin-bottom: var(--sp-sm);
            color: var(--c5);
            border-bottom: 1px solid color-mix(in srgb, var(--c3) 50%, transparent);
            cursor: pointer;
            transition: var(--trans)
        }

        .buildings-list li:hover {
            background: color-mix(in srgb, var(--c3) 30%, transparent);
            transform: translateX(5px);
            color: var(--c6)
        }

        .buildings-list li:last-child {
            margin-bottom: 0;
            border-bottom: none
        }

        .buildings-list li.selected {
            background: color-mix(in srgb, var(--c4) 30%, transparent);
            color: var(--c6);
            border-left: 3px solid var(--c5);
            padding-left: calc(var(--sp-sm) - 3px)
        }

        .enter-location {
            margin-top: var(--sp-md);
            padding: var(--sp-sm) var(--sp-md);
            background: linear-gradient(135deg, var(--c4), var(--c5));
            border: none;
            border-radius: var(--radius);
            color: var(--c1);
            font-size: var(--fs-base);
            text-align: center;
            cursor: pointer;
            transition: var(--trans);
            width: 100%
        }

        .enter-location:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px color-mix(in srgb, var(--c5) 40%, transparent)
        }

        .enter-location:active {
            transform: scale(.98)
        }

        .map-menu {
            position: absolute;
            bottom: var(--sp-lg);
            right: var(--sp-md);
            display: flex;
            flex-direction: column;
            align-items: center;
            pointer-events: auto;
            z-index: 4
        }

        .map-menu div {
            width: 150px;
            padding: var(--sp-md);
            background: linear-gradient(135deg, var(--c3), var(--c2));
            border: 1px solid var(--c4);
            border-radius: var(--radius);
            color: var(--c6);
            cursor: pointer;
            transition: var(--trans);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: var(--fw-m);
            text-align: center
        }

        .map-menu div:hover {
            background: linear-gradient(135deg, var(--c4), var(--c3));
            transform: translateX(5px);
            box-shadow: 0 2px 8px color-mix(in srgb, var(--c5) 30%, transparent)
        }

        .map-menu div:active {
            transform: translateX(5px) scale(.98)
        }

        @keyframes fade-in {
            from {
                opacity: 0;
                transform: translateY(10px)
            }

            to {
                opacity: 1;
                transform: translateY(0)
            }
        }

        .map-info-panel,
        .location-info,
        .map-menu {
            animation: fade-in .5s ease-out
        }



        /* 主页区域 */
        .Main {
            position: relative;
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            grid-template-rows: auto repeat(9, 1fr);
            gap: 0;
            margin: var(--sp-lg) 0 0 15%;
            width: 80%;
            height: 90%;
            padding: var(--sp-sm);
        }

        .C-Date {
            grid-area: 1/1/2/4;
            display: flex;
            align-items: center;
            padding: 0 var(--sp-lg);
            background: linear-gradient(90deg, color-mix(in srgb, var(--c2) 50%, transparent), transparent);
            border-bottom: 1px solid var(--c3);
            position: relative;
            overflow: hidden
        }

        .C-Date::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -10%;
            width: 120%;
            height: 200%;
            background: radial-gradient(circle, color-mix(in srgb, var(--c4) 10%, transparent), transparent);
            animation: float 8s ease-in-out infinite
        }

        .date-content {
            display: flex;
            align-items: center;
            gap: var(--sp-lg);
            z-index: 1
        }

        .date-icon {
            font-size: var(--fs-lg);
            color: var(--c5);
            animation: pulse-glow 2s ease-in-out infinite
        }

        .date-text {
            display: flex;
            flex-direction: column;
            gap: var(--sp-sm)
        }

        .date-secondary {
            font-size: var(--fs-sm);
            color: var(--c5);
            opacity: .8
        }

        .C-Event {
            grid-area: 2/1/11/4;
            padding: var(--sp-lg);
            background: color-mix(in srgb, var(--c1) 30%, transparent);
            border-right: 2px solid transparent;
            border-image: linear-gradient(180deg, var(--c3), transparent) 1;
            position: relative
        }

        .event-container {
            height: 100%;
            overflow-y: auto;
            padding-right: var(--sp-sm)
        }

        .event-header {
            position: sticky;
            top: 0;
            background: linear-gradient(180deg, var(--c1), transparent);
            padding: var(--sp-sm) 0 var(--sp-lg);
            margin-bottom: var(--sp-md);
            z-index: 1
        }

        .event-title {
            font-size: var(--fs-lg);
            color: var(--c6);
            font-weight: var(--fw-b);
            display: flex;
            align-items: center;
            gap: var(--sp-sm)
        }

        .event-title::before {
            content: '';
            width: 4px;
            height: 1.2em;
            background: linear-gradient(180deg, var(--c5), var(--c4));
            border-radius: 2px
        }

        .event-content p {
            margin: 0 0 var(--sp-md) 0;
            line-height: 1.8;
            color: var(--c5)
        }

        .ap-label {
            font-size: var(--fs-base);
            color: var(--c5)
        }

        .ap-value {
            font-size: var(--fs-xl);
            font-weight: var(--fw-b);
            color: var(--c6);
            position: relative;
            padding: 0 var(--sp-xl);
            margin: 0 var(--sp-xl)
        }

        .ap-value::before,
        .ap-value::after {
            content: '';
            position: absolute;
            width: 30px;
            height: 30px;
            border: 2px solid var(--c4);
            border-radius: 50%;
            top: 50%;
            transform: translateY(-50%)
        }

        .ap-value::before {
            left: -10px;
            background: radial-gradient(circle, color-mix(in srgb, var(--c4) 50%, transparent), transparent);
            animation: orbit 3s linear infinite
        }

        .ap-value::after {
            right: -15px;
            background: radial-gradient(circle, var(--c5), transparent);
            animation: orbit 3s linear infinite reverse
        }

        .C-Layoutscene {
            grid-area: 2/4/7/5;
            padding: var(--sp-lg);
            background: linear-gradient(135deg, color-mix(in srgb, var(--c2) 30%, transparent), transparent);
            border-bottom: 1px solid var(--c3);
            position: relative;
            overflow: hidden
        }

        .scene-wrapper {
            height: 100%;
            display: flex;
            flex-direction: column
        }

        .scene-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: var(--sp-md)
        }

        .scene-title {
            font-size: var(--fs-md);
            color: var(--c6);
            font-weight: var(--fw-m);
            display: flex;
            align-items: center;
            gap: var(--sp-sm);
            white-space: nowrap
        }

        .scene-icon {
            width: 30px;
            height: 30px;
            background: radial-gradient(circle, var(--c4), var(--c3));
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--c6);
            font-size: var(--fs-sm)
        }

        .scene-content {
            flex: 1;
            overflow-y: auto;
            padding-right: var(--sp-sm);
            line-height: 1.6
        }

        .scene-content span.interactive {
            color: var(--c6);
            cursor: pointer;
            transition: var(--trans)
        }

        .scene-content span.interactive:hover {
            color: var(--c5);
            text-shadow: 0 0 5px color-mix(in srgb, var(--c5) 30%, transparent)
        }

        .C-Datail {
            grid-area: 7/4/11/5;
            padding: var(--sp-lg);
            background: linear-gradient(180deg, transparent, color-mix(in srgb, var(--c2) 40%, transparent));
            position: relative
        }

        .detail-wrapper {
            height: 100%;
            display: flex;
            flex-direction: column
        }

        .detail-header {
            margin-bottom: var(--sp-md);
            padding-bottom: var(--sp-sm);
            border-bottom: 1px solid var(--c3)
        }

        .detail-title {
            font-size: var(--fs-md);
            color: var(--c6);
            font-weight: var(--fw-m)
        }

        .detail-content {
            flex: 1;
            overflow-y: auto;
            padding-right: var(--sp-sm);
            color: var(--c5);
            line-height: 1.6
        }

        .detail-content p {
            margin: 0 0 var(--sp-sm) 0
        }

        .floating-orb {
            position: absolute;
            width: 60px;
            height: 60px;
            background: radial-gradient(circle, color-mix(in srgb, var(--c4) 30%, transparent), transparent);
            border-radius: 50%;
            pointer-events: none;
            animation: float-around 15s infinite
        }

        .floating-orb:nth-child(1) {
            top: 20%;
            left: 80%;
            animation-delay: 0s
        }

        .floating-orb:nth-child(2) {
            top: 60%;
            left: 85%;
            animation-delay: 5s
        }

        /* 线索页区域 */
        .Clue {
            display: none;
            position: relative;
            margin: var(--sp-lg) 0 0 15%;
            width: 80%;
            height: 90%;
            border-radius: 0 0 1rem 0;
            border: 1px solid var(--c3)
        }

        .clue-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            grid-template-rows: repeat(7, 1fr);
            height: calc(100% - 40px);
            gap: 10px;
            padding: var(--sp-md)
        }

        .trust-value {
            grid-area: 1/5/2/6;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background: radial-gradient(circle, color-mix(in srgb, var(--c4) 30%, transparent), color-mix(in srgb, var(--c2) 80%, transparent));
            border-radius: 50%;
            border: 2px solid var(--c4)
        }

        .trust-label {
            font-size: var(--fs-sm);
            color: var(--c5)
        }

        .trust-number {
            font-size: var(--fs-xl);
            font-weight: var(--fw-b);
            color: var(--c6);
            text-shadow: 0 0 10px color-mix(in srgb, var(--c6) 50%, transparent)
        }

        .progress-container {
            grid-area: 1/1/2/5;
            display: flex;
            align-items: center;
            padding: 0 var(--sp-md)
        }

        .progress-track {
            width: 100%;
            height: 20px;
            background: color-mix(in srgb, var(--c2) 50%, transparent);
            border-radius: 10px;
            border: 1px solid var(--c3);
            position: relative;
            overflow: hidden
        }

        .progress-fill {
            height: 100%;
            width: 75%;
            background: linear-gradient(90deg, var(--c4), var(--c5));
            border-radius: 10px;
            transition: width .5s ease;
            box-shadow: 0 0 20px color-mix(in srgb, var(--c5) 30%, transparent)
        }

        .progress-markers {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 100%;
            display: flex;
            justify-content: space-evenly;
            padding: 0 20px
        }

        .marker {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: var(--c2);
            border: 2px solid var(--c4);
            transition: all .3s
        }

        .marker.active {
            background: var(--c6);
            box-shadow: 0 0 15px var(--c6);
            transform: scale(1.3)
        }

        .clue-list {
            grid-area: 2/1/8/4;
            background: color-mix(in srgb, var(--c1) 50%, transparent);
            border: 1px solid var(--c3);
            border-radius: var(--radius);
            padding: var(--sp-md);
            overflow-y: auto
        }

        .clue-list h3 {
            margin-bottom: var(--sp-md);
            padding-bottom: var(--sp-sm);
            border-bottom: 1px solid var(--c3)
        }

        .clue-category {
            margin-bottom: var(--sp-sm)
        }

        .category-header {
            display: flex;
            align-items: center;
            gap: var(--sp-sm);
            padding: var(--sp-sm);
            cursor: pointer;
            transition: var(--trans);
            border-radius: 4px
        }

        .category-header:hover {
            background: color-mix(in srgb, var(--c3) 20%, transparent)
        }

        .category-header i {
            font-size: var(--fs-xs);
            transition: transform .3s
        }

        .category-header.expanded i {
            transform: rotate(90deg)
        }

        .count {
            margin-left: auto;
            background: var(--c3);
            padding: 2px 8px;
            border-radius: 10px;
            font-size: var(--fs-xs)
        }

        .clue-items {
            padding-left: var(--sp-lg);
            max-height: 0;
            overflow: hidden;
            transition: max-height .3s
        }

        .category-header.expanded+.clue-items {
            max-height: 200px
        }

        .clue-item {
            padding: var(--sp-sm);
            margin: 4px 0;
            cursor: pointer;
            border-radius: 4px;
            transition: var(--trans);
            font-size: var(--fs-sm)
        }

        .clue-item:hover {
            background: color-mix(in srgb, var(--c3) 30%, transparent);
            transform: translateX(5px)
        }

        .clue-item.active {
            background: color-mix(in srgb, var(--c4) 30%, transparent);
            border-left: 3px solid var(--c5);
            color: var(--c6)
        }

        .clue-item.selected {
            background: color-mix(in srgb, var(--c5) 20%, transparent);
            border: 1px solid var(--c5);
        }

        .level-3 .category-header {
            color: #8b7355
        }

        .level-2 .category-header {
            color: #c0c0c0
        }

        .level-1 .category-header {
            color: #ffd700
        }

        .analyze-button {
            grid-area: 7/4/8/6;
            display: flex;
            align-items: center;
            justify-content: center
        }

        .btn-analyze {
            width: 80%;
            height: 60%;
            background: linear-gradient(135deg, var(--c4), var(--c3));
            border: 2px solid var(--c5);
            border-radius: var(--radius);
            color: var(--c6);
            font-size: var(--fs-lg);
            font-weight: var(--fw-b);
            cursor: pointer;
            transition: var(--trans);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--sp-sm)
        }

        .btn-analyze:hover {
            background: linear-gradient(135deg, var(--c5), var(--c4));
            transform: scale(1.05);
            box-shadow: 0 5px 20px color-mix(in srgb, var(--c5) 40%, transparent)
        }

        .btn-analyze:active {
            transform: scale(.98)
        }

        .btn-analyze:disabled {
            background: var(--c3);
            border-color: var(--c4);
            color: var(--c4);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .clue-detail {
            grid-area: 2/4/7/6;
            background: color-mix(in srgb, var(--c2) 30%, transparent);
            border: 1px solid var(--c3);
            border-radius: var(--radius);
            padding: var(--sp-md);
            display: flex;
            flex-direction: column;
            gap: var(--sp-md)
        }

        .detail-text {
            max-height: 100%;
            overflow-y: auto
        }

        .detail-text h3 {
            color: var(--c6);
            margin-bottom: var(--sp-sm)
        }

        .detail-text p {
            font-size: var(--fs-sm);
            line-height: var(--lh-n);
            color: var(--c5)
        }

        .selection-info {
            background: color-mix(in srgb, var(--c3) 30%, transparent);
            border-radius: var(--radius);
            padding: var(--sp-sm);
            font-size: var(--fs-sm);
            color: var(--c5);
            margin-top: auto;
            text-align: center;
        }

        .Store {
            display: none;
            position: absolute;
            top: var(--sp-lg);
            left: 15%;
            width: 80%;
            height: 90%;
            padding: var(--sp-md)
        }

        .store-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            grid-template-rows: repeat(8, 1fr);
            gap: var(--sp-sm);
            height: 100%;
            position: relative;
            overflow: hidden;
        }

        .currency-display {
            grid-area: 1/1/2/2;
            background: radial-gradient(circle, color-mix(in srgb, var(--c4) 30%, transparent), var(--c2));
            border-radius: var(--radius);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            border: 1px solid var(--c4)
        }

        .store-items {
            grid-area: 1/4/6/6;
            background: color-mix(in srgb, var(--c1) 50%, transparent);
            border: 1px solid var(--c3);
            border-radius: var(--radius);
            padding: var(--sp-md);
            overflow-y: auto
        }

        .item-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--sp-sm)
        }

        .store-item {
            aspect-ratio: 1;
            background: color-mix(in srgb, var(--c2) 50%, transparent);
            border: 1px solid var(--c3);
            border-radius: var(--radius);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--trans);
            position: relative
        }

        .store-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px color-mix(in srgb, var(--c5) 30%, transparent);
            border-color: var(--c5)
        }

        .store-item.selected {
            border-color: var(--c6);
            background: color-mix(in srgb, var(--c4) 30%, transparent)
        }

        .item-icon {
            font-size: 2rem;
            color: var(--c5);
            margin-bottom: var(--sp-sm)
        }

        .item-name {
            font-size: var(--fs-sm);
            color: var(--c5);
            text-align: center
        }

        .item-price {
            position: absolute;
            bottom: 5px;
            right: 5px;
            background: var(--c3);
            padding: 2px 6px;
            border-radius: 4px;
            font-size: var(--fs-xs);
            color: var(--c6)
        }

        .item-detail {
            grid-area: 6/4/9/6;
            background: color-mix(in srgb, var(--c2) 30%, transparent);
            border: 1px solid var(--c3);
            border-radius: var(--radius);
            padding: var(--sp-md);
            overflow-y: auto;
            max-height: 100%
        }

        .merchant-area {
            grid-area: 7/1/9/4;
            background: linear-gradient(135deg, color-mix(in srgb, var(--c2) 40%, transparent), transparent);
            border: 1px solid var(--c3);
            border-radius: var(--radius);
            padding: var(--sp-md);
            display: block;
            gap: var(--sp-md)
        }

        #item-description {
            display: flex;
            flex-direction: column;
            gap: var(--sp-sm)
        }

        #item-description h4 {
            margin: 0;
            color: var(--c6);
            font-size: var(--fs-md)
        }

        #item-description p {
            margin: 0;
            line-height: 1.5
        }

        #item-description button {
            margin-top: var(--sp-sm);
            background: linear-gradient(135deg, var(--c4), var(--c5));
            border: none;
            border-radius: var(--radius);
            padding: var(--sp-sm) var(--sp-md);
            color: var(--c1);
            font-size: var(--fs-sm);
            cursor: pointer;
            transition: var(--trans);
            font-weight: bold;
            width: fit-content
        }

        #item-description button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, .3)
        }

        .btn-purchase {
            width: 80%;
            padding: var(--sp-md);
            background: linear-gradient(135deg, var(--c4), var(--c5));
            border: none;
            border-radius: var(--radius);
            color: var(--c1);
            font-size: var(--fs-md);
            font-weight: var(--fw-b);
            cursor: pointer;
            transition: var(--trans);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--sp-sm);
        }

        .btn-purchase:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px color-mix(in srgb, var(--c5) 40%, transparent);
        }

        .btn-purchase:active {
            transform: scale(.98);
        }

        .btn-purchase:disabled {
            background: var(--c3);
            color: var(--c4);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        /* 档案页区域 */
        .File {
            display: none;
            position: relative;
            margin: var(--sp-lg) 0 0 15%;
            width: 100%;
            height: 100%;
            padding: var(--sp-md)
        }

        .file-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            grid-template-rows: repeat(7, 1fr);
            gap: var(--sp-md);
            width: 80%;
            height: 90%;
        }

        .profile-section {
            grid-area: 1/4/5/6;
            background: color-mix(in srgb, var(--c2) 30%, transparent);
            border: 1px solid var(--c3);
            border-radius: var(--radius);
            padding: var(--sp-md);
            overflow-y: auto
        }

        .profile-header {
            display: flex;
            align-items: center;
            gap: var(--sp-lg);
            margin-bottom: var(--sp-lg);
            padding-bottom: var(--sp-md);
            border-bottom: 1px solid var(--c3)
        }

        .profile-avatar {
            width: auto;
            height: 100%;
            background: radial-gradient(circle, var(--c4), var(--c3));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: var(--c6)
        }

        .profile-info h2 {
            margin-bottom: var(--sp-sm)
        }

        .attributes-section {
            margin-bottom: var(--sp-lg)
        }

        .attribute-row {
            display: flex;
            align-items: center;
            margin-bottom: var(--sp-sm)
        }

        .attribute-label {
            width: 60px;
            color: var(--c5)
        }

        .attribute-bar {
            flex: 1;
            height: 8px;
            background: color-mix(in srgb, var(--c2) 50%, transparent);
            border-radius: 4px;
            margin: 0 var(--sp-sm);
            position: relative
        }

        .attribute-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--c4), var(--c5));
            border-radius: 4px;
            transition: width .3s
        }

        .attribute-value {
            width: 40px;
            text-align: right;
            color: var(--c6);
            font-weight: var(--fw-m)
        }

        /* 属性说明区域 */
        .attribute-description {
            grid-area: 5/4/8/6;
            background: color-mix(in srgb, var(--c1) 50%, transparent);
            border: 1px solid var(--c3);
            border-radius: var(--radius);
            padding: var(--sp-md);
            overflow-y: auto
        }

        .attribute-description h3 {
            color: var(--c6);
            margin-bottom: var(--sp-md);
            border-bottom: 1px solid var(--c3);
            padding-bottom: var(--sp-sm);
        }

        .attribute-detail {
            display: none;
            animation: fade-in 0.3s ease-out;
        }

        .attribute-detail.active {
            display: block;
        }

        .attribute-detail h4 {
            color: var(--c5);
            margin: 0 0 var(--sp-sm) 0;
            font-size: var(--fs-md);
        }

        .attribute-detail p {
            font-size: var(--fs-sm);
            line-height: var(--lh-n);
            color: var(--c5);
            margin: 0 0 var(--sp-md) 0;
        }

        .records-section {
            grid-area: 1/1/8/4;
            background: color-mix(in srgb, var(--c1) 50%, transparent);
            border: 1px solid var(--c3);
            border-radius: var(--radius);
            padding: var(--sp-lg);
            overflow-y: auto
        }

        .record-entry {
            padding: var(--sp-sm);
            border-bottom: 1px solid var(--c3);
            font-size: var(--fs-sm);
            color: var(--c5)
        }

        .record-entry:last-child {
            border-bottom: none
        }

        /* 设置页区域 */
        .Setup {
            display: none;
            flex-direction: column;
            position: relative;
            margin: var(--sp-lg) 0 0 15%;
            width: 80%;
            height: 90%;
            border-radius: 0 0 1rem 0;
            border: 1px solid var(--c3)
        }

        .content-header {
            width: 15%;
            position: fixed;
            background: color-mix(in srgb, var(--c2) 80%, transparent);
            z-index: 5;
            color: var(--c5);
            font-size: var(--fs-lg);
            font-weight: var(--fw-m);
            display: flex;
            align-items: center;
            clip-path: polygon(0 0, 100% 0, 65% 100%, 0% 100%);
            padding: var(--sp-sm) var(--sp-lg)
        }

        .child-content {
            flex: 1;
            overflow-y: auto;
            padding: 0
        }

        .form-section {
            margin: var(--sp-md);
            padding: var(--sp-md);
            border-radius: 8px;
            border: 1px solid var(--c4)
        }

        .attribute-section {
            background: color-mix(in srgb, var(--c4) 30%, transparent)
        }

        .character-section {
            background: color-mix(in srgb, var(--c2) 50%, transparent)
        }

        .ai-section {
            background: color-mix(in srgb, var(--c5) 30%, transparent)
        }

        .theme-section {
            background: color-mix(in srgb, var(--c4) 30%, transparent)
        }

        .dice-section {
            background: color-mix(in srgb, var(--c2) 50%, transparent)
        }

        .form-input,
        .form-textarea,
        .custom-select select {
            width: 98%;
            padding: var(--sp-sm);
            background: color-mix(in srgb, var(--c1) 50%, transparent);
            border: 1px solid var(--c4);
            color: var(--c5);
            border-radius: 4px;
            font: var(--fs-sm)/var(--lh-n) inherit
        }

        .form-textarea {
            height: 8vh;
            resize: vertical
        }

        .custom-select {
            position: relative;
            display: inline-block;
            width: 100%
        }

        .custom-select select {
            width: 100%;
            padding-right: 30px;
            appearance: none;
            cursor: pointer;
            transition: var(--trans)
        }

        .custom-select::after {
            content: '▼';
            position: absolute;
            top: 50%;
            right: 12px;
            transform: translateY(-50%);
            color: var(--c5);
            pointer-events: none;
            font-size: var(--fs-xs);
            transition: transform .3s, color .3s
        }

        .custom-select:hover select {
            background: color-mix(in srgb, var(--c3) 50%, transparent);
            border-color: var(--c4);
            color: var(--c6)
        }

        .custom-select:hover::after {
            color: var(--c6)
        }

        .custom-select:focus-within::after {
            transform: translateY(-50%) rotate(180deg);
            color: var(--c6)
        }

        input[type="radio"] {
            display: none
        }

        .custom-radio {
            display: inline-block;
            width: 1rem;
            height: 1rem;
            background: var(--c1);
            border: .0625rem solid var(--c3);
            border-radius: .125rem;
            margin-right: .5rem;
            position: relative;
            vertical-align: middle;
            transition: var(--trans)
        }

        input[type="radio"]:checked+.custom-radio {
            background: var(--c3);
            border-color: var(--c4)
        }

        input[type="radio"]:checked+.custom-radio::after {
            content: '';
            position: absolute;
            inset: 15%;
            background: var(--c6);
            border-radius: .0625rem
        }

        .btn-primary,
        .attr-btn {
            border: 1px solid var(--c4);
            color: var(--c5);
            border-radius: 8px;
            cursor: pointer;
            transition: var(--trans)
        }

        .btn-primary {
            padding: 1.5% 4%;
            background: linear-gradient(135deg, var(--c4), var(--c2));
            font-size: var(--fs-base);
            font-weight: var(--fw-m)
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--c4), var(--c3));
            color: var(--c6)
        }

        .attr-btn {
            width: 30px;
            height: 30px;
            background: color-mix(in srgb, var(--c1) 50%, transparent);
            border-radius: 4px
        }

        /* 动画效果 */
        @keyframes float {

            0%,
            100% {
                transform: translateY(0) scale(1)
            }

            50% {
                transform: translateY(-20px) scale(1.1)
            }
        }

        @keyframes pulse-glow {

            0%,
            100% {
                opacity: .6;
                text-shadow: 0 0 5px color-mix(in srgb, var(--c5) 50%, transparent)
            }

            50% {
                opacity: 1;
                text-shadow: 0 0 15px var(--c5)
            }
        }

        @keyframes orbit {
            0% {
                transform: translateY(-50%) scale(1) rotate(0deg)
            }

            50% {
                transform: translateY(-50%) scale(1.2) rotate(180deg)
            }

            100% {
                transform: translateY(-50%) scale(1) rotate(360deg)
            }
        }

        @keyframes float-around {

            0%,
            100% {
                transform: translate(0, 0) scale(1);
                opacity: .3
            }

            25% {
                transform: translate(-30px, 20px) scale(1.2);
                opacity: .5
            }

            50% {
                transform: translate(20px, -30px) scale(.8);
                opacity: .3
            }
        }

        @keyframes pulse {
            0% {
                transform: scale(1)
            }

            50% {
                transform: scale(1.1)
            }

            100% {
                transform: scale(1)
            }
        }

        /* Toast通知 */
        .toast {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: color-mix(in srgb, var(--c4) 80%, transparent);
            color: var(--c6);
            padding: 10px 20px;
            border-radius: var(--radius);
            box-shadow: 0 4px 12px rgba(0, 0, 0, .3);
            z-index: 1000;
            display: none;
            animation: toast-in 0.3s ease-out;
        }

        @keyframes toast-in {
            from {
                transform: translate(-50%, 100%);
                opacity: 0;
            }

            to {
                transform: translate(-50%, 0);
                opacity: 1;
            }
        }

        /* 线索选择状态 */
        .clue-item::before {
            content: '';
            display: inline-block;
            width: 0;
            height: 0;
            margin-right: 0;
            transition: all 0.3s;
        }

        .clue-item.selected::before {
            content: '•';
            width: 10px;
            margin-right: 5px;
            color: var(--c6);
        }

        .C-Date,
        .C-Event,
        .C-Layoutscene,
        .C-Datail,
        .clue-list,
        .clue-detail,
        .profile-section,
        .attribute-description,
        .records-section,
        .store-items,
        .item-detail,
        .merchant-area,
        .form-section {
            box-shadow: 0 0 1px 1px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(2px);
        }

        .Content {
            box-shadow: 0 0 1px 1px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(1px);
        }
    </style>
</head>

<body class="theme-dark">
    <div id="dashboard-container">
        <div class="map-ui"></div>
        <div class="PingYongmap">
            <iframe src="https://pingyong.achrombin.workers.dev" loading="lazy"></iframe>
        </div>

        <!-- 地图区域 -->
        <div class="map">
            <div class="location-info">
                <div class="date-primary">2024年3月15日 星期五</div>
                <div class="current-location">
                    <i class="fas fa-map-marker-alt location-icon"></i>
                    <span>古堡</span>
                </div>
            </div>

            <div class="map-info-panel">
                <!-- AP部分 -->
                <div class="action-points-section">
                    <div class="action-points">
                        <span>AP</span>
                        <span>3</span>
                    </div>
                </div>

                <!-- 地图描述 -->
                <div class="map-description">
                    <div class="title">地点描述</div>
                    <p>地点介绍</p>
                </div>

                <!-- 建筑物部分 -->
                <div class="map-buildings">
                    <div class="sub-location">主要建筑:</div>
                    <ul class="buildings-list">
                        <li data-building="hall">大厅</li>
                        <li data-building="library">图书馆</li>
                        <li data-building="tower">塔楼</li>
                    </ul>
                    <button class="enter-location">前往</button>
                </div>
            </div>

            <div class="map-menu">
                <div onclick="backToMain()">返回主介面</div>
            </div>
        </div>


        <div class="leftside-menu">
            <div class="avatar">
                <div class="icon">
                    <p>|</p>
                </div>
            </div>
            <nav class="menu">
                <li class="active" data-menu="Main">主页</li>
                <li data-menu="Clue">线索</li>
                <li data-menu="File">档案</li>
                <li data-menu="Store">商店</li>
                <li data-menu="Setup">设定</li>
            </nav>
        </div>
        <div class="map-menu"></div>
        <div class="Content">
            <!-- 主页区域 -->
            <div class="Main">
                <div class="C-Date">
                    <div class="date-content">
                        <i class="fas fa-calendar-alt date-icon"></i>
                        <div class="date-text">
                            <b class="date-primary">2024年3月15日 星期五</b>
                            <b class="date-secondary">古堡大厅 - 黄昏时分</b>
                        </div>
                    </div>
                </div>
                <div class="C-Event">
                    <div class="event-container">
                        <div class="event-header">
                            <div class="event-title"><b>当前剧情</b></div>
                        </div>
                        <div class="event-content">
                            <p>夕阳的馀晖透过彩色玻璃窗洒进大厅，将整个空间染成了诡异的绯红色。你站在巨大的水晶吊灯下，四周的阴影似乎都在蠢蠢欲动。

                                管家刚才的话还在你耳边迴响："主人已经三天没有出现了，而那个房间...从里面传来了奇怪的声音。"

                                你握紧了手中的提灯，决定要探索这座神秘的古堡。但首先，你需要决定接下来的行动...</p>
                        </div>
                    </div>
                </div>
                <div class="action-points-wrapper">
                    <div class="action-points">
                        <span class="ap-label">AP</span>
                        <span class="ap-value">3</span>
                    </div>
                </div>
                <div class="C-Layoutscene">
                    <div class="scene-wrapper">
                        <div class="scene-header">
                            <div class="scene-title">
                                <b>场景概要</b>
                            </div>
                        </div>
                        <div class="scene-content">
                            客房里有<span class="interactive" data-item="两张床">两张床</span>，一把椅子和一张<span class="interactive"
                                data-item="桌子">桌子</span>，两张床之间有<span class="interactive"
                                data-item="架子">架子</span>。另外，虽然有<span class="interactive" data-item="浴室">浴室</span>和<span
                                class="interactive" data-item="卫生间">卫生间</span>，但<span class="interactive"
                                data-item="墙壁">墙壁</span>是玻璃墙，可以从外面拉下遮挡视线的<span class="interactive"
                                data-item="百叶窗">百叶窗</span>。墙上有很大的窗户，只要拉开窗帘，就能看到夕阳照耀下的<span class="interactive"
                                data-item="大海">大海</span>和<span class="interactive" data-item="港口">港口</span>，<span
                                class="interactive" data-item="服务生">服务生</span>站在门口一旁。
                        </div>
                    </div>
                </div>
                <div class="C-Datail">
                    <div class="detail-wrapper">
                        <div class="detail-header">
                            <b>场景细节</b>
                        </div>
                        <div class="detail-content" id="scene-detail">
                            <p>点击场景概要中的物品以查看详情</p>
                        </div>
                    </div>
                </div>
                <div class="floating-orb"></div>
                <div class="floating-orb"></div>
            </div>

            <!-- 线索区域 -->
            <div class="Clue">
                <div class="content-header">线索</div>
                <div class="clue-grid">
                    <div class="trust-value">
                        <span class="trust-label">信赖值</span>
                        <span class="trust-number">75</span>
                    </div>
                    <div class="progress-container">
                        <div class="progress-track">
                            <div class="progress-fill"></div>
                            <div class="progress-markers">
                                <div class="marker" data-stage="1"></div>
                                <div class="marker" data-stage="2"></div>
                                <div class="marker" data-stage="3"></div>
                                <div class="marker" data-stage="4"></div>
                                <div class="marker active" data-stage="5"></div>
                            </div>
                        </div>
                    </div>
                    <div class="clue-list">
                        <h3>思维</h3>
                        <div class="clue-category level-3">
                            <div class="category-header">
                                <i class="fas fa-chevron-right"></i>
                                <span>线索</span>
                                <span class="count">5</span>
                            </div>
                            <div class="clue-items">
                                <div class="clue-item" data-level="3">破旧的日记本</div>
                                <div class="clue-item" data-level="3">褪色的照片</div>
                                <div class="clue-item" data-level="3">神秘的钥匙</div>
                                <div class="clue-item" data-level="3">奇怪的符号</div>
                                <div class="clue-item" data-level="3">残破的地图</div>
                            </div>
                        </div>
                        <div class="clue-category level-2">
                            <div class="category-header">
                                <i class="fas fa-chevron-right"></i>
                                <span>猜想</span>
                                <span class="count">3</span>
                            </div>
                            <div class="clue-items">
                                <div class="clue-item active" data-level="2">神秘的信件</div>
                                <div class="clue-item" data-level="2">古老的地图</div>
                                <div class="clue-item" data-level="2">密室的入口</div>
                            </div>
                        </div>
                        <div class="clue-category level-1">
                            <div class="category-header">
                                <i class="fas fa-chevron-right"></i>
                                <span>证据</span>
                                <span class="count">1</span>
                            </div>
                            <div class="clue-items">
                                <div class="clue-item" data-level="1">关键证据</div>
                            </div>
                        </div>
                    </div>
                    <div class="analyze-button">
                        <button class="btn-analyze" id="btn-analyze" disabled>
                            <i class="fas fa-search"></i>
                            推理
                        </button>
                    </div>
                    <div class="clue-detail">
                        <div class="detail-text">
                            <h3 id="clue-title">神秘的信件</h3>
                            <p id="clue-description">一封没有署名的信件，纸张已经泛黄，上面的字迹依然清晰可见。信中提到了一个神秘的地点和一串奇怪的数字。</p>
                        </div>
                        <div class="selection-info" id="selection-info">
                            选择两个同级线索进行推理合成 (已选择: <span id="selected-count">0</span>/2)
                        </div>
                    </div>
                </div>
                <div class="toast" id="synthesis-toast"></div>
            </div>

            <!-- 档案区域 -->
            <div class="File">
                <div class="content-header">档案</div>
                <div class="file-grid">
                    <div class="profile-section">
                        <div class="profile-header">
                            <div class="profile-avatar">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="profile-info">
                                <h2>调查员</h2>
                                <p style="color:var(--c5);">王子</p>
                            </div>
                        </div>
                        <div class="attributes-section">
                            <div class="attribute-row" data-attr="body">
                                <span class="attribute-label">体魄:</span>
                                <div class="attribute-bar">
                                    <div class="attribute-fill" style="width:60%"></div>
                                </div>
                                <span class="attribute-value">60</span>
                            </div>
                            <div class="attribute-row" data-attr="knowledge">
                                <span class="attribute-label">学识:</span>
                                <div class="attribute-bar">
                                    <div class="attribute-fill" style="width:75%"></div>
                                </div>
                                <span class="attribute-value">75</span>
                            </div>
                            <div class="attribute-row" data-attr="skill">
                                <span class="attribute-label">技巧:</span>
                                <div class="attribute-bar">
                                    <div class="attribute-fill" style="width:45%"></div>
                                </div>
                                <span class="attribute-value">45</span>
                            </div>
                            <div class="attribute-row" data-attr="charm">
                                <span class="attribute-label">魅力:</span>
                                <div class="attribute-bar">
                                    <div class="attribute-fill" style="width:55%"></div>
                                </div>
                                <span class="attribute-value">55</span>
                            </div>
                            <div class="attribute-row" data-attr="trust">
                                <span class="attribute-label">信赖值:</span>
                                <div class="attribute-bar">
                                    <div class="attribute-fill" style="width:75%"></div>
                                </div>
                                <span class="attribute-value">75</span>
                            </div>
                        </div>
                    </div>
                    <!-- 属性说明区域 -->
                    <div class="attribute-description">
                        <h3>属性说明</h3>
                        <div class="attribute-detail" id="body-detail">
                            <h4>体魄</h4>
                            <p>体魄代表角色的身体素质和耐力。高体魄能够承受更多伤害，并在体力检定中获得优势。</p>
                            <p>示例技能: 徒手格斗、攀爬、游泳、负重行走</p>
                        </div>
                        <div class="attribute-detail" id="knowledge-detail">
                            <h4>学识</h4>
                            <p>学识代表角色的知识储备和理解能力。高学识能够更好地理解复杂信息，解读线索和古籍。</p>
                            <p>示例技能: 历史学、自然科学、医学知识、神秘学</p>
                        </div>
                        <div class="attribute-detail" id="skill-detail">
                            <h4>技巧</h4>
                            <p>技巧代表角色的灵巧度和精细操作能力。高技巧有利于开锁、解密和各种需要手部精细动作的工作。</p>
                            <p>示例技能: 锁匠、机械维修、潜行、手工制作</p>
                        </div>
                        <div class="attribute-detail" id="charm-detail">
                            <h4>魅力</h4>
                            <p>魅力代表角色的社交能力和个人魅力。高魅力有助于说服他人、获取信息和建立人际关系。</p>
                            <p>示例技能: 说服、恐吓、欺骗、交涉</p>
                        </div>
                        <div class="attribute-detail" id="trust-detail">
                            <h4>信赖值</h4>
                            <p>信赖值代表与NPC的关系程度。高信赖值可以获得更多帮助和隐藏信息，解锁特殊对话选项。</p>
                            <p>提升方法: 完成任务、提供帮助、选择符合NPC期望的对话选项</p>
                        </div>
                    </div>
                    <div class="records-section">
                        <h3>重要记录</h3>
                        <div class="record-entry">
                            3月15日 上午 - 古堡大厅 - 管家提到主人失踪三天
                        </div>
                        <div class="record-entry">
                            3月15日 下午 - 客房 - 发现神秘信件
                        </div>
                        <div class="record-entry">
                            3月15日 晚上 - 图书馆 - 找到与古堡历史相关的书籍
                        </div>
                        <div class="record-entry">
                            3月16日 凌晨 - 塔楼 - 听到奇怪的声音
                        </div>
                        <div class="record-entry">
                            3月16日 上午 - 地下室 - 发现隐藏的通道
                        </div>
                    </div>
                </div>
            </div>

            <!-- 商店页区域 -->
            <div class="Store">
                <div class="content-header">商店</div>
                <div class="store-grid">
                    <div class="purchase-button"
                        style="grid-area: 6/3/7/4; display: flex; align-items: center; justify-content: center;">
                        <button id="buy-item-btn" class="btn-purchase">兑换</button>
                    </div>
                    <div class="currency-display">
                        <span class="trust-label">货币</span>
                        <span class="trust-number">1500</span>
                    </div>
                    <div class="store-items">
                        <h3>商品列表</h3>
                        <div class="item-grid">
                            <div class="store-item" data-item="healing-potion">
                                <i class="fas fa-flask item-icon"></i>
                                <span class="item-name">治疗药水</span>
                                <span class="item-price">50</span>
                            </div>
                            <div class="store-item" data-item="torch">
                                <i class="fas fa-fire item-icon"></i>
                                <span class="item-name">火把</span>
                                <span class="item-price">20</span>
                            </div>
                            <div class="store-item" data-item="map">
                                <i class="fas fa-map item-icon"></i>
                                <span class="item-name">古堡地图</span>
                                <span class="item-price">200</span>
                            </div>
                            <div class="store-item" data-item="key">
                                <i class="fas fa-key item-icon"></i>
                                <span class="item-name">神秘钥匙</span>
                                <span class="item-price">500</span>
                            </div>
                            <div class="store-item" data-item="compass">
                                <i class="fas fa-compass item-icon"></i>
                                <span class="item-name">罗盘</span>
                                <span class="item-price">150</span>
                            </div>
                            <div class="store-item" data-item="rope">
                                <i class="fas fa-link item-icon"></i>
                                <span class="item-name">绳索</span>
                                <span class="item-price">80</span>
                            </div>
                        </div>
                    </div>
                    <div class="item-detail">
                        <div id="item-description">
                            <p>选择商品查看详情</p>
                        </div>
                    </div>
                    <div class="merchant-area" id="merchant-text">
                        欢迎光临！需要什么帮助吗？
                    </div>
                </div>
            </div>

            <!-- 设置区域 -->
            <div class="Setup">
                <div class="content-header">设置</div>
                <div class="child-content">
                    <div class="form-section attribute-section">
                        <h3>属性分配 总点数:<span id="total-points">240</span></h3>
                        <div
                            style="display:grid;grid-template-columns:1fr 2fr 40px;gap:1%;align-items:center;font-size:var(--fs-sm)">
                            <label>体魄:</label>
                            <div style="display:flex;align-items:center;gap:8px">
                                <button class="attr-btn" onclick="CA('body',-5)">-</button>
                                <div
                                    style="flex:1;height:6px;background:color-mix(in srgb,var(--c1) 30%,transparent);border-radius:3px;position:relative;border:1px solid var(--c4)">
                                    <div id="body-bar"
                                        style="width:0%;height:100%;background:linear-gradient(90deg,var(--c4),var(--c5));border-radius:3px;transition:width .3s">
                                    </div>
                                </div>
                                <button class="attr-btn" onclick="CA('body',5)">+</button>
                            </div>
                            <span id="body-value" style="color:var(--c5);font-weight:bold">30</span>
                            <label>学识:</label>
                            <div style="display:flex;align-items:center;gap:8px">
                                <button class="attr-btn" onclick="CA('knowledge',-5)">-</button>
                                <div
                                    style="flex:1;height:6px;background:color-mix(in srgb,var(--c1) 30%,transparent);border-radius:3px;position:relative;border:1px solid var(--c4)">
                                    <div id="knowledge-bar"
                                        style="width:0%;height:100%;background:linear-gradient(90deg,var(--c4),var(--c5));border-radius:3px;transition:width .3s">
                                    </div>
                                </div>
                                <button class="attr-btn" onclick="CA('knowledge',5)">+</button>
                            </div>
                            <span id="knowledge-value" style="color:var(--c5);font-weight:bold">30</span>
                            <label>技巧:</label>
                            <div style="display:flex;align-items:center;gap:8px">
                                <button class="attr-btn" onclick="CA('skill',-5)">-</button>
                                <div
                                    style="flex:1;height:6px;background:color-mix(in srgb,var(--c1) 30%,transparent);border-radius:3px;position:relative;border:1px solid var(--c4)">
                                    <div id="skill-bar"
                                        style="width:0%;height:100%;background:linear-gradient(90deg,var(--c4),var(--c5));border-radius:3px;transition:width .3s">
                                    </div>
                                </div>
                                <button class="attr-btn" onclick="CA('skill',5)">+</button>
                            </div>
                            <span id="skill-value" style="color:var(--c5);font-weight:bold">30</span>
                            <label>魅力:</label>
                            <div style="display:flex;align-items:center;gap:8px">
                                <button class="attr-btn" onclick="CA('charm',-5)">-</button>
                                <div
                                    style="flex:1;height:6px;background:color-mix(in srgb,var(--c1) 30%,transparent);border-radius:3px;position:relative;border:1px solid var(--c4)">
                                    <div id="charm-bar"
                                        style="width:0%;height:100%;background:linear-gradient(90deg,var(--c4),var(--c5));border-radius:3px;transition:width .3s">
                                    </div>
                                </div>
                                <button class="attr-btn" onclick="CA('charm',5)">+</button>
                            </div>
                            <span id="charm-value" style="color:var(--c5);font-weight:bold">30</span>
                        </div>
                    </div>
                    <div class="form-section character-section">
                        <h3>自定义角色</h3>
                        <div style="margin-bottom:1%">
                            <label style="display:block;margin-bottom:0.5%;font-size:var(--fs-sm)">姓名:</label>
                            <input type="text" placeholder="输入角色姓名" class="form-input">
                        </div>
                        <div style="margin-bottom:1.5%">
                            <label style="display:block;margin-bottom:0.5%;font-size:var(--fs-sm)">描述:</label>
                            <textarea placeholder="输入角色描述" class="form-textarea"></textarea>
                        </div>
                        <div>
                            <label style="display:block;margin-bottom:1%;font-size:var(--fs-sm)">好感度阶段设定:</label>
                            <div style="display:grid;grid-template-columns:60px 1fr;gap:0.8%;font-size:var(--fs-sm)">
                                <span>20%:</span><input type="text" placeholder="好感度20时的变化" class="form-input">
                                <span>40%:</span><input type="text" placeholder="好感度40时的变化" class="form-input">
                                <span>60%:</span><input type="text" placeholder="好感度60时的变化" class="form-input">
                                <span>80%:</span><input type="text" placeholder="好感度80时的变化" class="form-input">
                                <span>100%:</span><input type="text" placeholder="好感度100时的变化" class="form-input">
                            </div>
                        </div>
                    </div>
                    <div class="form-section ai-section">
                        <h3>副AI设定</h3>
                        <div style="display:grid;gap:1%">
                            <div>
                                <label style="display:block;margin-bottom:0.5%;font-size:var(--fs-sm)">LLM
                                    SOURCE:</label>
                                <div class="custom-select">
                                    <select>
                                        <option>OpenAI</option>
                                        <option>Claude</option>
                                        <option>Gemini</option>
                                        <option>本地模型</option>
                                    </select>
                                </div>
                            </div>
                            <div>
                                <label style="display:block;margin-bottom:0.5%;font-size:var(--fs-sm)">BASE URL:</label>
                                <input type="text" placeholder="输入API基础网址" class="form-input">
                            </div>
                            <div>
                                <label style="display:block;margin-bottom:0.5%;font-size:var(--fs-sm)">TOKEN:</label>
                                <input type="password" placeholder="输入API密钥" class="form-input">
                            </div>
                        </div>
                    </div>
                    <div style="display:grid;grid-template-columns:1fr 1fr;gap:2%;margin:2%">
                        <div class="form-section theme-section">
                            <h3>主题</h3>
                            <div style="display:flex;flex-direction:column;gap:0.8%">
                                <label style="cursor:pointer;padding:0.5%;font-size:var(--fs-sm)">
                                    <input type="radio" name="theme" value="dark" checked>
                                    <span class="custom-radio"></span>暗面
                                </label>
                                <label style="cursor:pointer;padding:0.5%;font-size:var(--fs-sm)">
                                    <input type="radio" name="theme" value="light">
                                    <span class="custom-radio"></span>小白
                                </label>
                                <label style="cursor:pointer;padding:0.5%;font-size:var(--fs-sm)">
                                    <input type="radio" name="theme" value="brown">
                                    <span class="custom-radio"></span>棕色
                                </label>
                            </div>
                        </div>
                        <div class="form-section dice-section">
                            <h3>骰子设定</h3>
                            <div style="display:flex;flex-direction:column;gap:0.8%">
                                <label style="cursor:pointer;padding:0.5%;font-size:var(--fs-sm)">
                                    <input type="radio" name="dice" value="on" checked>
                                    <span class="custom-radio"></span>开
                                </label>
                                <label style="cursor:pointer;padding:0.5%;font-size:var(--fs-sm)">
                                    <input type="radio" name="dice" value="off">
                                    <span class="custom-radio"></span>关
                                </label>
                            </div>
                        </div>
                    </div>
                    <div style="text-align:center;margin:2%;padding-bottom:2%">
                        <button class="btn-primary" id="save-settings-btn">保存设置</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
const App = (() => {
    // ==================== 配置与数据模块 ====================
    const CONFIG = {
        attributes: { body: 30, knowledge: 30, skill: 30, charm: 30 },
        config: { maxPoints: 240, minAttr: 30, maxAttr: 90 },
        
        itemInfos: {
            'healing-potion': {
                description: '一瓶散发着淡淡草药香气的红色液体。饮用后可恢复少量生命值，对轻伤非常有效。',
                dialogue: '这瓶治疗药水是用最新鲜的草药熬制的。冒险者出行必备，一口下去伤口就能立刻愈合！'
            },
            'torch': {
                description: '一根结实的木棒，顶端缠绕着浸油的布条。点燃后可以照亮黑暗的区域。',
                dialogue: '这种火把燃烧稳定，不会轻易熄灭。在黑暗的地方，这可是救命的东西啊！'
            },
            'map': {
                description: '一张绘制精细的古堡平面图，标注了主要房间和走廊。部分区域看起来被特意模糊处理。',
                dialogue: '这地图可不便宜，但物有所值！有了它，你就不会在那座迷宫般的古堡里迷路了。'
            },
            'key': {
                description: '一把做工精良的古董钥匙，由黄铜制成，上面刻有神秘的符文。据说能打开古堡中的某个秘密房间。',
                dialogue: '这把钥匙...说实话我也不确定它到底能开什么门。但有人愿意出高价收购，它肯定有特别之处。'
            },
            'compass': {
                description: '一个精致的罗盘，指针似乎不仅会指向北方，有时还会指向其他方向，彷佛被某种未知力量吸引。',
                dialogue: '这个罗盘可不一般，它好像能感应到某些...特别的东西。'
            },
            'rope': {
                description: '一捆坚韧的麻绳，约15米长，能承受相当大的重量。攀爬或捆绑物品时非常实用。',
                dialogue: '看起来普通，但这绳子经过特殊处理，非常结实！无论是攀爬悬崖还是从窗户逃生，都能派上用场。'
            }
        },

        sceneItemDetails: {
            '两张床': '两张单人床，白色的床铺和枕头，看起来很柔软。',
            '桌子': '普通的桌子，摆放着鲜花和欢迎卡。',
            '架子': '床头架子上放着台灯和一本旅游指南。',
            '浴室': '干净明亮的浴室，配有浴缸和淋浴设施。',
            '卫生间': '与浴室相连的卫生间，设备齐全。',
            '墙壁': '浴室的墙壁是透明玻璃制成，从房间可以直接看到里面。',
            '百叶窗': '可以控制的百叶窗，可以提供隐私保护。',
            '大海': '窗外是一望无际的蔚蓝大海，波光粼粼。',
            '港口': '远处可以看到繁忙的港口，有许多船只进出。',
            '服务生': '身穿整齐制服的服务生，随时等待为客人提供帮助。'
        },

        clueData: {
            '破旧的日记本': { level: 3, description: '一本残破的日记，页面泛黄，记载着某人的日常生活。有些页面被撕掉了，似乎隐藏着什么秘密。' },
            '褪色的照片': { level: 3, description: '一张老旧的黑白照片，上面是一个陌生的家庭。背景是一栋古老的建筑，看起来有些诡异。' },
            '神秘的钥匙': { level: 3, description: '一把做工精细的古老钥匙，上面刻有奇怪的符号，不知道能打开什么锁。' },
            '奇怪的符号': { level: 3, description: '墙上刻着的一系列神秘符号，看起来像是某种古老的文字或仪式标记。' },
            '残破的地图': { level: 3, description: '一张被撕成碎片的地图，上面标记着古堡的某个区域，但缺少了关键部分。' },
            '神秘的信件': { level: 2, description: '一封没有署名的信件，纸张已经泛黄，上面的字迹依然清晰可见。信中提到了一个神秘的地点和一串奇怪的数字。' },
            '古老的地图': { level: 2, description: '一张手绘的地图，标记着几个未知的地点。地图的边缘有一些神秘的符号，可能是某种暗号。' },
            '密室的入口': { level: 2, description: '根据收集到的线索，古堡内应该存在一个隐藏的密室，入口可能与书架或壁炉有关。' },
            '关键证据': { level: 1, description: '这是破解整个谜团的关键！一个小小的物件，却蕴含着巨大的秘密。需要仔细推敲才能发现其中的玄机。' }
        },

        synthesisResults: {
            '破旧的日记本+褪色的照片': {
                result: '神秘的信件',
                description: '通过对比日记本中的记录和照片上的人物，你发现了一封隐藏的信件，上面提到了一个秘密地点。'
            },
            '神秘的钥匙+奇怪的符号': {
                result: '古老的地图',
                description: '钥匙上的符号与墙上的标记相互呼应，拼凑在一起形成了一张指向特定地点的地图。'
            },
            '残破的地图+神秘的钥匙': {
                result: '密室的入口',
                description: '将钥匙与地图上的标记对照，你推断出古堡内应该存在一个隐藏的密室入口。'
            },
            '神秘的信件+古老的地图': {
                result: '关键证据',
                description: '将信件中提到的位置与地图对照，你发现了一个关键证据，这可能是解开整个谜团的钥匙！'
            },
            '古老的地图+密室的入口': {
                result: '关键证据',
                description: '根据地图和你对密室入口的推断，你找到了一个关键证据，这或许能揭示古堡的秘密。'
            },
            '神秘的信件+密室的入口': {
                result: '关键证据',
                description: '信件中的线索与密室入口的发现相互印证，引导你找到了破解谜团的关键证据。'
            }
        }
    };

    // ==================== DOM选择器模块 ====================
    const DOM = {
        menuItems: document.querySelectorAll('.menu li'),
        sections: document.querySelectorAll('.Main,.Clue,.File,.Setup,.Store')
    };

    // ==================== 状态管理模块 ====================
    const STATE = {
        selectedClues: []
    };

    // ==================== 工具函数模块 ====================
    const Utils = {
        showToast: (message, duration = 3000) => {
            let toast = document.getElementById('toast-notification');
            if (!toast) {
                toast = document.createElement('div');
                toast.id = 'toast-notification';
                toast.className = 'toast';
                document.body.appendChild(toast);
            }
            toast.textContent = message;
            toast.style.display = 'block';
            setTimeout(() => toast.style.display = 'none', duration);
        }
    };

    // ==================== 属性系统模块 ====================
    const AttributeSystem = {
        changeAttr: (type, value) => {
            const newValue = CONFIG.attributes[type] + value;
            const totalPoints = Object.values(CONFIG.attributes).reduce((sum, x) => sum + x, 0);
          
            if (newValue >= CONFIG.config.minAttr && newValue <= CONFIG.config.maxAttr && totalPoints + value <= CONFIG.config.maxPoints) {
                CONFIG.attributes[type] = newValue;
                document.getElementById(type + '-value').textContent = newValue;
                document.getElementById(type + '-bar').style.width = (newValue - CONFIG.config.minAttr) / (CONFIG.config.maxAttr - CONFIG.config.minAttr) * 100 + '%';
                document.getElementById('total-points').textContent = CONFIG.config.maxPoints - totalPoints - value;
            }
        }
    };

    // ==================== 线索系统模块 ====================
    const ClueSystem = {
        updateSelectedCluesInfo: () => {
            document.getElementById('selected-count').textContent = STATE.selectedClues.length;
            const btnAnalyze = document.getElementById('btn-analyze');
          
            if (STATE.selectedClues.length === 2) {
                const level1 = CONFIG.clueData[STATE.selectedClues[0]].level;
                const level2 = CONFIG.clueData[STATE.selectedClues[1]].level;
                btnAnalyze.disabled = level1 !== level2;
            } else {
                btnAnalyze.disabled = true;
            }
        },

        synthesizeClues: () => {
            if (STATE.selectedClues.length !== 2) return;
          
            const [clue1, clue2] = STATE.selectedClues;
            if (CONFIG.clueData[clue1].level !== CONFIG.clueData[clue2].level) {
                Utils.showToast('只能合成相同级别的线索！');
                return;
            }

            let result = CONFIG.synthesisResults[`${clue1}+${clue2}`] || CONFIG.synthesisResults[`${clue2}+${clue1}`];
          
            if (result) {
                Utils.showToast(`合成成功！${clue1} + ${clue2} = ${result.result}`);
                document.querySelectorAll('.clue-item.selected').forEach(item => item.classList.remove('selected'));
                STATE.selectedClues = [];
                ClueSystem.updateSelectedCluesInfo();
            } else {
                Utils.showToast('这两个线索无法组合成有意义的推理');
            }
        }
    };

    // ==================== 菜单导航模块 ====================
    const MenuSystem = {
        initMenuNavigation: () => {
            DOM.menuItems.forEach(item => {
                item.addEventListener('click', (e) => {
                    DOM.menuItems.forEach(x => x.classList.remove('active'));
                    e.currentTarget.classList.add('active');
                    DOM.sections.forEach(x => x.style.display = 'none');
                  
                    const target = document.querySelector(`.${e.currentTarget.getAttribute('data-menu')}`);
                    if (target) {
                        const displayMode = { Setup: 'flex', Store: 'grid', Main: 'grid' }[e.currentTarget.getAttribute('data-menu')] || 'block';
                        target.style.display = displayMode;
                    }
                });
            });
        }
    };

    // ==================== 主题系统模块 ====================
    const ThemeSystem = {
        initTheme: () => {
            document.querySelectorAll('input[name="theme"]').forEach(radio => {
                radio.addEventListener('change', (e) => {
                    if (e.target.checked) {
                        ['theme-dark', 'theme-light', 'theme-brown'].forEach(theme => document.body.classList.remove(theme));
                        document.body.classList.add('theme-' + e.target.value);

                        const iframe = document.querySelector('iframe');
                        if (iframe) {
                            iframe.contentWindow.postMessage({
                                type: 'theme-change',
                                theme: e.target.value
                            }, '*');
                        }
                    }
                });
            });
        }
    };

    // ==================== 地图系统模块 ====================
const MapSystem = {
    initMap: () => {
        const mapUI = document.querySelector('.map-ui');
        const mapElement = document.querySelector('.map');
        const content = document.querySelector('.Content');
        const leftMenu = document.querySelector('.leftside-menu');
        const pingYongmap = document.querySelector('.PingYongmap');
        const iframe = document.querySelector('.PingYongmap iframe');

        if (mapUI) {
            mapUI.addEventListener('click', () => {
                content.style.display = 'none';
                leftMenu.style.display = 'none';
                mapUI.style.display = 'none';
                mapElement.style.display = 'block';
                iframe.style.pointerEvents = 'auto';
                pingYongmap.style.zIndex = '3';
            });
        }
    },

    backToMain: () => {
        const content = document.querySelector('.Content');
        const leftMenu = document.querySelector('.leftside-menu');
        const mapUI = document.querySelector('.map-ui');
        const mapElement = document.querySelector('.map');
        const iframe = document.querySelector('.PingYongmap iframe');
        const pingYongmap = document.querySelector('.PingYongmap');
      
        content.style.display = 'block';
        leftMenu.style.display = 'block';
        mapUI.style.display = 'block';
        mapElement.style.display = 'none';
        iframe.style.pointerEvents = 'none';
        pingYongmap.style.zIndex = '1';
      
        iframe.contentWindow.postMessage({ type: 'backToMain' }, '*');
    }
};

// 区域数据配置
const areaData = {
    WestNorth: {
        title: "西城区域",
        description: "传统工业区保留筒子楼式工人宿舍群，红砖建筑与工厂烟囱构成主要天际线。冬季依靠燃煤锅炉集中供暖，社区配给站供应基础生活物资。",
        buildings: ["金正淑纺织厂", "西城工人浴场", "红星电影院"]
    },
    North: {
        title: "牡丹峰区域",
        description: "自然山体与文体设施融合区，金日成体育场依山而建。森林公园设指定野餐区，部分步道实行季节性封闭养护。",
        buildings: ["绫罗岛啤酒馆", "文殊院", "牡丹峰卡拉OK厅"]
    },
    WWest: {
        title: "普通江区域",
        description: "自然山体与文体设施融合区，金日成体育场依山而建。森林公园设指定野餐区，部分步道实行季节性封闭养护。",
        buildings: ["银河娱乐中心", "金日成综合大学", "科学家大街"]
    },
    NNorth: {
        title: "柳京泰洞区域",
        description: "国家机关集中地，地标性建筑柳京饭店以105层混凝土结构成为世界最高空置建筑。周边高层公务员住宅区配备独立供电系统，主干道每日由专用清洁车维护。",
        buildings: ["高丽酒店", "平壤地铁站", "党中央委员会大楼"]
    },
    EastNorth: {
        title: "船桥区域",
        description: "涉外专属区包含72洞高尔夫球场及外交官公寓群。服务场所通行美元、欧元等外币结算，区域电网独立于城市主网运行。",
        buildings: ["海棠花馆", "船桥餐厅", "平壤高尔夫球场"]
    },
    EastSouth: {
        title: "大同江区域",
        description: "城市文化景观核心带，主体思想塔等花岗岩纪念碑矗立江畔。观光游船主要接待外籍游客，沿江餐厅多设有双语菜单，水质监测浮标常年漂浮于江面。",
        buildings: ["平壤综合医院", "玉流馆冷麵店", "主体思想塔"]
    },
    South: {
        title: "乐浪区域",
        description: "科技研发机构聚集区，平壤科技大学校园内配备卫星通信设备。研究人员居住的欧式公寓楼群与水上乐园相邻，形成特殊生活社区。",
        buildings: ["平壤科学技术大学", "乐浪水上乐园", "国际友谊俱乐部"]
    },
    WestSouth: {
        title: "万景台",
        description: "位于平壤西南郊的历史纪念区，以金日成出生地及复原的19世纪农舍为核心景观。区内古树成荫，现存低矮砖房民居与革命遗迹保护区交错分布，部分区域仍维持传统生活风貌。",
        buildings: ["万景台温泉浴场", "万景台革命医院", "金日成故居"]
    }
};

// 监听 iframe 消息
window.addEventListener('message', function(event) {
    if (event.data.type === 'areaSelected') {
        updateMapInfo(event.data.area);
    }
    
    // 添加 hover 处理
    if (event.data.type === 'areaHovered') {
        updateMapInfo(event.data.area);
    }
});

// 更新地图信息
function updateMapInfo(areaClass) {
    const data = areaData[areaClass];
    if (!data) return;
    
    // 更新标题
    const titleElement = document.querySelector('.map-description .title');
    if (titleElement) {
        titleElement.textContent = data.title;
    }
    
    // 更新描述
    const descriptionElement = document.querySelector('.map-description p');
    if (descriptionElement) {
        descriptionElement.textContent = data.description;
    }
    
    // 更新建筑列表
    const buildingsList = document.querySelector('.buildings-list');
    if (buildingsList) {
        buildingsList.innerHTML = '';
        data.buildings.forEach(building => {
            const li = document.createElement('li');
            li.textContent = building;
            buildingsList.appendChild(li);
        });
    }
}

// 初始化默认显示
document.addEventListener('DOMContentLoaded', function() {
    updateMapInfo('North');
});



    // ==================== 场景系统模块 ====================
    const SceneSystem = {
        initScene: () => {
            document.querySelectorAll('.scene-content .interactive').forEach(item => {
                item.addEventListener('click', () => {
                    const itemName = item.getAttribute('data-item');
                    document.getElementById('scene-detail').innerHTML = `<p>${itemName}: ${CONFIG.sceneItemDetails[itemName] || '无详细信息'}</p>`;
                    document.querySelectorAll('.scene-content .interactive').forEach(i => i.style.textShadow = '');
                    item.style.textShadow = '0 0 8px var(--c6)';
                });
            });
        }
    };

    // ==================== 商店系统模块 ====================
    const StoreSystem = {
        initStore: () => {
            const buyButton = document.getElementById('buy-item-btn');
            if (!buyButton) return;
            
            buyButton.disabled = true;
          
            document.querySelectorAll('.store-item').forEach(item => {
                item.addEventListener('click', function() {
                    document.querySelectorAll('.store-item').forEach(i => i.classList.remove('selected'));
                    this.classList.add('selected');
                  
                    const itemId = this.getAttribute('data-item');
                    const info = CONFIG.itemInfos[itemId];
                  
                    if (info) {
                        document.getElementById('item-description').innerHTML = `<h4>${this.querySelector('.item-name').textContent}</h4><p>${info.description}</p>`;
                        document.querySelector('.merchant-area').textContent = info.dialogue;
                        buyButton.disabled = false;
                        buyButton.setAttribute('data-selected-item', itemId);
                        buyButton.setAttribute('data-price', this.querySelector('.item-price').textContent);
                    }
                });
            });

            buyButton.addEventListener('click', function() {
                if (this.disabled) return;
              
                const itemId = this.getAttribute('data-selected-item');
                const price = parseInt(this.getAttribute('data-price'));
                const currentMoney = parseInt(document.querySelector('.currency-display .trust-number').textContent);
              
                if (currentMoney >= price) {
                    document.querySelector('.currency-display .trust-number').textContent = currentMoney - price;
                    Utils.showToast(`成功购买了 ${document.querySelector(`.store-item[data-item="${itemId}"] .item-name`).textContent}！`, 2000);
                    document.querySelector('.merchant-area').textContent = "谢谢惠顾！这是个不错的选择。";
                } else {
                    Utils.showToast("资金不足，无法购买！", 2000);
                    document.querySelector('.merchant-area').textContent = "啊，看起来你的钱不够啊。也许下次吧...";
                }
            });
        }
    };

    // ==================== 事件绑定模块 ====================
    const EventSystem = {
        initClueEvents: () => {
            // 线索类别切换
            document.querySelectorAll('.category-header').forEach(header => {
                header.addEventListener('click', () => header.classList.toggle('expanded'));
            });

            // 线索选择事件
            document.querySelectorAll('.clue-item').forEach(item => {
                item.addEventListener('click', () => {
                    const clueName = item.textContent;
                    const clue = CONFIG.clueData[clueName];
                  
                    document.querySelectorAll('.clue-item.active').forEach(i => i.classList.remove('active'));
                    item.classList.add('active');
                  
                    document.getElementById('clue-title').textContent = clueName;
                    document.getElementById('clue-description').textContent = clue.description;
                  
                    if (item.classList.contains('selected')) {
                        item.classList.remove('selected');
                        STATE.selectedClues = STATE.selectedClues.filter(c => c !== clueName);
                    } else {
                        const sameLevel = STATE.selectedClues.filter(c => CONFIG.clueData[c].level === clue.level);
                        if (sameLevel.length < 2) {
                            item.classList.add('selected');
                            STATE.selectedClues.push(clueName);
                        } else {
                            const firstSelected = sameLevel[0];
                            document.querySelectorAll('.clue-item').forEach(i => {
                                if (i.textContent === firstSelected) i.classList.remove('selected');
                            });
                            STATE.selectedClues = STATE.selectedClues.filter(c => c !== firstSelected);
                            item.classList.add('selected');
                            STATE.selectedClues.push(clueName);
                        }
                    }
                    ClueSystem.updateSelectedCluesInfo();
                });
            });

            // 推理按钮事件
            const btnAnalyze = document.getElementById('btn-analyze');
            if (btnAnalyze) {
                btnAnalyze.addEventListener('click', (e) => {
                    if (!e.currentTarget.disabled) {
                        e.currentTarget.style.animation = 'pulse 0.6s';
                        setTimeout(() => e.currentTarget.style.animation = '', 600);
                        ClueSystem.synthesizeClues();
                    }
                });
            }
        },

        initFileEvents: () => {
            // 档案页属性点击事件
            document.querySelectorAll('.attribute-row').forEach(row => {
                row.addEventListener('click', function() {
                    const attr = this.getAttribute('data-attr');
                    document.querySelectorAll('.attribute-detail').forEach(detail => detail.classList.remove('active'));
                    document.getElementById(`${attr}-detail`).classList.add('active');
                });
            });
        }
    };

    // ==================== 初始化模块 ====================
    const Init = {
        initDefaults: () => {
            // 初始化属性
            Object.keys(CONFIG.attributes).forEach(type => AttributeSystem.changeAttr(type, 0));
          
            // 设置默认主题
            const darkTheme = document.querySelector('input[name="theme"][value="dark"]');
            if (darkTheme) darkTheme.checked = true;
          
            // 初始化线索类别展开
            const firstCategory = document.querySelector('.category-header');
            if (firstCategory) firstCategory.classList.add('expanded');
          
            // 初始化档案页第一个属性说明
            const firstDetail = document.getElementById('body-detail');
            if (firstDetail) firstDetail.classList.add('active');
        },

        bindAllEvents: () => {
            MenuSystem.initMenuNavigation();
            ThemeSystem.initTheme();
            MapSystem.initMap();
            SceneSystem.initScene();
            StoreSystem.initStore();
            EventSystem.initClueEvents();
            EventSystem.initFileEvents();
        },

        start: () => {
            // 暴露全局函数
            window.CA = window.changeAttr = AttributeSystem.changeAttr;
            window.backToMain = MapSystem.backToMain;

            Init.initDefaults();
            Init.bindAllEvents();
        }
    };

    // ==================== 主程序入口 ====================
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', Init.start);
    } else {
        Init.start();
    }

    // 返回公开API
    return { 
        changeAttr: AttributeSystem.changeAttr, 
        showToast: Utils.showToast 
    };
})();

    </script>
</body>

</html>