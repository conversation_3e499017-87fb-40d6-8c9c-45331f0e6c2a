/**
 * SillyTavern HTML Embed Extension - Client Side
 * Allows embedding interactive HTML/JS content in chat messages
 */

import { eventSource, event_types, getRequestHeaders, saveSettingsDebounced, converter, reloadMarkdownProcessor } from '../../../script.js';
import { extension_settings, getContext, renderExtensionTemplateAsync } from '../../extensions.js';
import { callGenericPopup, POPUP_TYPE } from '../../popup.js';

// Extension settings
const defaultSettings = {
    enabled: true,
    sandboxMode: true,
    allowedDomains: [],
    maxTemplateSize: 100000, // 100KB limit
    enableInlineHtml: false,
    enableTemplateSystem: true,
    strictSecurity: true, // Enable strict security validation
    allowExternalResources: false, // Allow external scripts, stylesheets, etc.
    allowDangerousFunctions: false // Allow eval(), Function(), etc.
};

// Initialize extension settings
if (!extension_settings.htmlEmbed) {
    extension_settings.htmlEmbed = { ...defaultSettings };
}

// Template cache
let templateCache = new Map();

// Storage for processed HTMLJS blocks
let htmljsBlockStorage = new Map();

// Regex patterns for detecting HTML embed markup
const HTML_EMBED_PATTERNS = {
    // [html:template_id] - Reference to stored template
    template: /\[html:([a-f0-9]{32})\]/gi,
    // [html-inline:...] - Inline HTML content (if enabled)
    inline: /\[html-inline:(.*?)\]/gis,
    // [html-file:filename] - Reference to HTML file (future feature)
    file: /\[html-file:([^\]]+)\]/gi,
    // ```HTMLJS ... ``` - Code block syntax for HTML/JS content
    codeblock: /```(?:HTMLJS|htmljs)\s*\n([\s\S]*?)\n```/gi
};

/**
 * Fetch template from server
 */
async function fetchTemplate(templateId) {
    if (templateCache.has(templateId)) {
        return templateCache.get(templateId);
    }

    try {
        const response = await fetch(`/api/plugins/html-embed/get-template/${templateId}`, {
            method: 'GET',
            headers: getRequestHeaders()
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        if (data.success && data.template) {
            templateCache.set(templateId, data.template);
            return data.template;
        } else {
            throw new Error('Invalid response format');
        }
    } catch (error) {
        console.error(`Failed to fetch HTML template ${templateId}:`, error);
        return null;
    }
}

/**
 * Create a sandboxed iframe for HTML content
 */
function createSandboxedIframe(html, templateId) {
    const iframe = document.createElement('iframe');
    iframe.className = 'html-embed-iframe';
    iframe.style.cssText = `
        width: 100%;
        min-height: 200px;
        border: 1px solid var(--SmartThemeBorderColor, #ccc);
        border-radius: 8px;
        background: var(--SmartThemeBodyColor, white);
        margin: 10px 0;
        resize: vertical;
        overflow: hidden;
    `;
    
    // Security attributes
    iframe.sandbox = 'allow-scripts allow-same-origin allow-forms allow-popups allow-modals';
    iframe.setAttribute('data-template-id', templateId || 'inline');
    
    // Create the HTML document for the iframe
    const iframeDoc = `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <style>
                body {
                    margin: 0;
                    padding: 10px;
                    font-family: var(--mainFontFamily, 'Noto Sans', sans-serif);
                    font-size: var(--mainFontSize, 14px);
                    color: var(--SmartThemeQuoteColor, #333);
                    background: transparent;
                    overflow-x: hidden;
                }
                * {
                    box-sizing: border-box;
                }
                /* Inherit some SillyTavern styling */
                button {
                    background: var(--SmartThemeBlurTintColor, #007bff);
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: inherit;
                }
                button:hover {
                    opacity: 0.8;
                }
                input, select, textarea {
                    background: var(--SmartThemeBodyColor, white);
                    color: var(--SmartThemeQuoteColor, #333);
                    border: 1px solid var(--SmartThemeBorderColor, #ccc);
                    padding: 6px 10px;
                    border-radius: 4px;
                    font-size: inherit;
                }
            </style>
        </head>
        <body>
            ${html}
            <script>
                // Auto-resize iframe to content
                function resizeIframe() {
                    const height = Math.max(document.body.scrollHeight, document.body.offsetHeight);
                    window.parent.postMessage({
                        type: 'resize',
                        height: height,
                        templateId: '${templateId || 'inline'}'
                    }, '*');
                }
                
                // Initial resize
                setTimeout(resizeIframe, 100);
                
                // Resize on content changes
                const observer = new MutationObserver(resizeIframe);
                observer.observe(document.body, {
                    childList: true,
                    subtree: true,
                    attributes: true
                });
                
                // Resize on window resize
                window.addEventListener('resize', resizeIframe);
                
                // Communication with parent window
                window.addEventListener('message', function(event) {
                    if (event.data.type === 'refresh') {
                        resizeIframe();
                    }
                });
            </script>
        </body>
        </html>
    `;
    
    // Set the iframe content
    iframe.onload = function() {
        iframe.contentDocument.open();
        iframe.contentDocument.write(iframeDoc);
        iframe.contentDocument.close();
    };
    
    return iframe;
}

/**
 * Handle iframe resize messages
 */
window.addEventListener('message', function(event) {
    if (event.data.type === 'resize') {
        const iframe = document.querySelector(`iframe[data-template-id="${event.data.templateId}"]`);
        if (iframe) {
            iframe.style.height = Math.max(event.data.height, 100) + 'px';
        }
    }
});

/**
 * Validate HTML content for security (client-side basic check)
 */
function isValidHtmlContent(html) {
    // If strict security is disabled, allow everything
    if (!extension_settings.htmlEmbed.strictSecurity) {
        console.log('Strict security disabled, allowing all HTML content');
        return true;
    }

    const issues = [];

    // Check for external resources if not allowed
    if (!extension_settings.htmlEmbed.allowExternalResources) {
        const externalPatterns = [
            { pattern: /<script[^>]*src\s*=\s*["'][^"']*["'][^>]*>/gi, name: 'External scripts' },
            { pattern: /javascript\s*:/gi, name: 'JavaScript URLs' },
            { pattern: /<iframe[^>]*src\s*=\s*["'][^"']*["'][^>]*>/gi, name: 'External iframes' },
            { pattern: /<object/gi, name: 'Object tags' },
            { pattern: /<embed/gi, name: 'Embed tags' },
            { pattern: /<link[^>]*href\s*=\s*["'][^"']*["'][^>]*>/gi, name: 'External stylesheets' },
            { pattern: /<form[^>]*action\s*=\s*["'][^"']*["'][^>]*>/gi, name: 'External form actions' }
        ];

        for (const { pattern, name } of externalPatterns) {
            if (pattern.test(html)) {
                issues.push(name);
            }
        }
    }

    // Check for dangerous functions if not allowed
    if (!extension_settings.htmlEmbed.allowDangerousFunctions) {
        if (html.includes('eval(')) {
            issues.push('eval() function');
        }
        if (html.includes('Function(')) {
            issues.push('Function() constructor');
        }
    }

    if (issues.length > 0) {
        console.warn('HTML content blocked due to security issues:', issues);
        return false;
    }

    return true;
}

/**
 * Showdown extension to process HTMLJS code blocks
 */
function htmljsShowdownExtension() {
    return [{
        type: 'lang',
        filter: function(text) {
            if (!extension_settings.htmlEmbed.enabled) {
                console.log('HTML Embed extension disabled, skipping HTMLJS processing');
                return text;
            }

            console.log('Processing text for HTMLJS blocks:', text.substring(0, 200) + '...');

            // Process HTMLJS code blocks before markdown processing
            const result = text.replace(/```(?:HTMLJS|htmljs)\s*\n([\s\S]*?)\n```/gi, function(match, htmlContent) {
                const trimmedContent = htmlContent.trim();
                const blockId = `htmljs_block_${Math.random().toString(36).substr(2, 9)}`;

                console.log(`Found HTMLJS block, creating placeholder with ID: ${blockId}`);
                console.log('HTML content preview:', trimmedContent.substring(0, 100) + '...');

                // Store the HTML content for later processing
                htmljsBlockStorage.set(blockId, {
                    content: trimmedContent,
                    original: match
                });

                // Return a placeholder that will be processed later
                const placeholder = `<div class="htmljs-placeholder" data-block-id="${blockId}">Loading interactive content...</div>`;
                console.log('Generated placeholder:', placeholder);
                return placeholder;
            });

            if (result !== text) {
                console.log('Text was modified by HTMLJS extension');
                console.log('Storage now contains:', Array.from(htmljsBlockStorage.keys()));
            }

            return result;
        }
    }];
}

/**
 * Process HTML embed markup in message text
 */
async function processHtmlEmbeds(messageText, messageElement) {
    if (!extension_settings.htmlEmbed.enabled) {
        return messageText;
    }

    let processedText = messageText;
    const embedPromises = [];

    // Process template references
    if (extension_settings.htmlEmbed.enableTemplateSystem) {
        const templateMatches = [...messageText.matchAll(HTML_EMBED_PATTERNS.template)];
        
        for (const match of templateMatches) {
            const templateId = match[1];
            const placeholder = `__HTML_EMBED_${templateId}__`;
            
            embedPromises.push(
                fetchTemplate(templateId).then(template => {
                    if (template) {
                        const iframe = createSandboxedIframe(template.originalHtml || template.html, templateId);
                        return { placeholder, element: iframe, original: match[0] };
                    } else {
                        return { 
                            placeholder, 
                            element: `<div class="html-embed-error">❌ Template not found: ${templateId}</div>`,
                            original: match[0]
                        };
                    }
                })
            );
            
            processedText = processedText.replace(match[0], placeholder);
        }
    }

    // Process inline HTML (if enabled)
    if (extension_settings.htmlEmbed.enableInlineHtml) {
        const inlineMatches = [...messageText.matchAll(HTML_EMBED_PATTERNS.inline)];

        for (const match of inlineMatches) {
            const htmlContent = match[1];
            const placeholder = `__HTML_EMBED_INLINE_${Math.random().toString(36).substr(2, 9)}__`;

            if (htmlContent.length <= extension_settings.htmlEmbed.maxTemplateSize) {
                const iframe = createSandboxedIframe(htmlContent);
                embedPromises.push(Promise.resolve({
                    placeholder,
                    element: iframe,
                    original: match[0]
                }));
            } else {
                embedPromises.push(Promise.resolve({
                    placeholder,
                    element: `<div class="html-embed-error">❌ HTML content too large (max ${extension_settings.htmlEmbed.maxTemplateSize} bytes)</div>`,
                    original: match[0]
                }));
            }

            processedText = processedText.replace(match[0], placeholder);
        }
    }

    // Process HTMLJS code blocks (always enabled for better UX)
    const codeblockMatches = [...messageText.matchAll(HTML_EMBED_PATTERNS.codeblock)];

    for (const match of codeblockMatches) {
        const htmlContent = match[1].trim();
        const placeholder = `__HTML_EMBED_CODEBLOCK_${Math.random().toString(36).substr(2, 9)}__`;

        if (htmlContent.length <= extension_settings.htmlEmbed.maxTemplateSize) {
            // Validate HTML content for security
            if (isValidHtmlContent(htmlContent)) {
                const iframe = createSandboxedIframe(htmlContent, `codeblock_${Date.now()}`);
                embedPromises.push(Promise.resolve({
                    placeholder,
                    element: iframe,
                    original: match[0]
                }));
            } else {
                embedPromises.push(Promise.resolve({
                    placeholder,
                    element: `<div class="html-embed-error">❌ HTML content contains potentially dangerous elements</div>`,
                    original: match[0]
                }));
            }
        } else {
            embedPromises.push(Promise.resolve({
                placeholder,
                element: `<div class="html-embed-error">❌ HTML content too large (max ${extension_settings.htmlEmbed.maxTemplateSize} bytes)</div>`,
                original: match[0]
            }));
        }

        processedText = processedText.replace(match[0], placeholder);
    }

    // Wait for all embeds to be processed
    if (embedPromises.length > 0) {
        const embeds = await Promise.all(embedPromises);
        
        // Replace placeholders with actual elements after message is rendered
        setTimeout(() => {
            const mesTextElement = messageElement.find('.mes_text');
            if (mesTextElement.length > 0) {
                let html = mesTextElement.html();
                
                embeds.forEach(embed => {
                    if (typeof embed.element === 'string') {
                        html = html.replace(embed.placeholder, embed.element);
                    } else {
                        // For DOM elements, we need to insert them after replacing with a temporary marker
                        const tempId = `temp_${Math.random().toString(36).substr(2, 9)}`;
                        html = html.replace(embed.placeholder, `<div id="${tempId}"></div>`);
                        
                        mesTextElement.html(html);
                        const tempElement = mesTextElement.find(`#${tempId}`);
                        if (tempElement.length > 0) {
                            tempElement.replaceWith(embed.element);
                        }
                        html = mesTextElement.html(); // Update html for next iteration
                    }
                });
                
                if (embeds.some(embed => typeof embed.element === 'string')) {
                    mesTextElement.html(html);
                }
            }
        }, 100);
    }

    return processedText;
}

/**
 * Message rendering event handler
 */
async function onMessageRendered(messageId) {
    if (!extension_settings.htmlEmbed.enabled) {
        return;
    }

    try {
        const context = getContext();
        const message = context.chat[messageId];
        const messageElement = $(`#chat [mesid="${messageId}"]`);

        if (message && messageElement.length > 0) {
            // Process traditional HTML embeds (templates and inline)
            await processHtmlEmbeds(message.mes, messageElement);

            // Process HTMLJS code block placeholders with a small delay to ensure DOM is ready
            setTimeout(async () => {
                await processHtmljsPlaceholders(messageElement);
            }, 100);
        }
    } catch (error) {
        console.error('Error processing HTML embeds:', error);
    }
}

/**
 * Process HTMLJS placeholders in the rendered message
 */
async function processHtmljsPlaceholders(messageElement) {
    // Look for placeholders with both possible class names
    const placeholders = messageElement.find('.htmljs-placeholder, [data-block-id]').filter('[data-block-id]');

    console.log(`Found ${placeholders.length} HTMLJS placeholders to process`);

    for (let i = 0; i < placeholders.length; i++) {
        const placeholder = $(placeholders[i]);
        const blockId = placeholder.attr('data-block-id') || placeholder.data('block-id');
        const blockData = htmljsBlockStorage.get(blockId);

        console.log(`Processing placeholder with blockId: ${blockId}`, blockData);

        if (blockData) {
            const htmlContent = blockData.content;

            if (htmlContent.length <= extension_settings.htmlEmbed.maxTemplateSize) {
                // Validate HTML content for security
                if (isValidHtmlContent(htmlContent)) {
                    const iframe = createSandboxedIframe(htmlContent, `codeblock_${blockId}`);
                    placeholder.replaceWith(iframe);
                    console.log(`Successfully replaced placeholder ${blockId} with iframe`);
                } else {
                    const errorHtml = `<div class="html-embed-error">❌ HTML content contains potentially dangerous elements</div>`;
                    placeholder.replaceWith(errorHtml);
                    console.log(`Replaced placeholder ${blockId} with security error`);
                }
            } else {
                const errorHtml = `<div class="html-embed-error">❌ HTML content too large (max ${extension_settings.htmlEmbed.maxTemplateSize} bytes)</div>`;
                placeholder.replaceWith(errorHtml);
                console.log(`Replaced placeholder ${blockId} with size error`);
            }

            // Clean up storage
            htmljsBlockStorage.delete(blockId);
        } else {
            console.warn(`No block data found for blockId: ${blockId}`);
            // Replace with error message if no data found
            placeholder.replaceWith(`<div class="html-embed-error">❌ HTMLJS block data not found</div>`);
        }
    }
}



/**
 * Initialize the extension
 */
async function init() {
    console.log('Initializing HTML Embed Extension...');

    // Add CSS styles
    const style = document.createElement('style');
    style.textContent = `
        .html-embed-iframe {
            transition: height 0.3s ease;
        }

        .html-embed-error {
            background: #ffebee;
            color: #c62828;
            padding: 10px;
            border-radius: 4px;
            border-left: 4px solid #c62828;
            margin: 10px 0;
            font-family: monospace;
        }

        .html-embed-controls {
            margin: 10px 0;
            padding: 10px;
            background: var(--SmartThemeBlurTintColor, #f5f5f5);
            border-radius: 4px;
            border: 1px solid var(--SmartThemeBorderColor, #ddd);
        }

        .htmljs-placeholder {
            display: none;
        }
    `;
    document.head.appendChild(style);

    // Register Showdown extension for HTMLJS code blocks
    // We need to reload the markdown processor to include our extension
    try {
        // Add our extension to the global showdown extensions
        if (typeof window.htmljsShowdownExtension === 'undefined') {
            window.htmljsShowdownExtension = htmljsShowdownExtension;
        }

        // Reload the markdown processor to include our extension
        reloadMarkdownProcessor();

        // Now add our extension
        if (converter) {
            converter.addExtension(htmljsShowdownExtension(), 'htmljs');
            console.log('HTML Embed Showdown extension registered and markdown processor reloaded');
        } else {
            console.warn('Converter not available after reload');
        }
    } catch (error) {
        console.error('Failed to register HTML Embed Showdown extension:', error);
    }

    // Register event handlers
    eventSource.on(event_types.CHARACTER_MESSAGE_RENDERED, onMessageRendered);
    eventSource.on(event_types.USER_MESSAGE_RENDERED, onMessageRendered);
    eventSource.on(event_types.MESSAGE_UPDATED, onMessageRendered);

    console.log('HTML Embed Extension initialized successfully');
}

// Global placeholder processor - runs periodically to catch any missed placeholders
function processAllHtmljsPlaceholders() {
    if (!extension_settings.htmlEmbed.enabled) {
        return;
    }

    const allPlaceholders = $('.htmljs-placeholder, [data-block-id]').filter('[data-block-id]');
    if (allPlaceholders.length > 0) {
        console.log(`Global processor found ${allPlaceholders.length} unprocessed HTMLJS placeholders`);

        allPlaceholders.each(async function() {
            const placeholder = $(this);
            const messageElement = placeholder.closest('.mes');
            if (messageElement.length > 0) {
                await processHtmljsPlaceholders(messageElement);
            }
        });
    }
}

// Debug function to analyze all code blocks on the page
function debugCodeBlocks() {
    console.log('=== HTML Embed Debug: Analyzing all code blocks ===');

    const allCodeBlocks = $('code');
    console.log(`Total code blocks found: ${allCodeBlocks.length}`);

    allCodeBlocks.each(function(index) {
        const codeBlock = $(this);
        const className = codeBlock.attr('class') || 'no-class';
        const textContent = codeBlock.text();
        const preElement = codeBlock.closest('pre');
        const preClass = preElement.attr('class') || 'no-class';

        console.log(`Code block ${index}:`, {
            codeClass: className,
            preClass: preClass,
            textLength: textContent.length,
            textPreview: textContent.substring(0, 100),
            hasHTML: textContent.includes('<'),
            hasScript: textContent.includes('<script'),
            hasDiv: textContent.includes('<div')
        });

        // Check if this looks like an HTMLJS block
        if (textContent.includes('<div') || textContent.includes('<script') || textContent.includes('<button')) {
            console.log(`🎯 Potential HTMLJS block found at index ${index}`);
        }
    });

    console.log('=== End Debug Analysis ===');
}

// Expose debug function globally
window.debugCodeBlocks = debugCodeBlocks;

// Alternative approach: Direct code block processing
function processHtmljsCodeBlocks() {
    if (!extension_settings.htmlEmbed.enabled) {
        return;
    }

    // Try multiple possible selectors for HTMLJS code blocks
    const possibleSelectors = [
        'code.custom-language-HTMLJS',
        'code.custom-language-htmljs',
        'code.language-HTMLJS',
        'code.language-htmljs',
        'code.custom-HTMLJS',
        'code.custom-htmljs',
        'code[class*="HTMLJS"]',
        'code[class*="htmljs"]',
        'pre code:contains("HTMLJS")',
        'pre[class*="HTMLJS"]',
        'pre[class*="htmljs"]'
    ];

    let codeBlocks = $();
    for (const selector of possibleSelectors) {
        try {
            const blocks = $(selector);
            if (blocks.length > 0) {
                console.log(`Found ${blocks.length} blocks with selector: ${selector}`);
                codeBlocks = codeBlocks.add(blocks);
            }
        } catch (e) {
            console.warn('Selector failed:', selector, e);
        }
    }

    // Search for code blocks that contain HTMLJS syntax
    $('pre code').each(function() {
        const codeElement = $(this);
        const className = codeElement.attr('class') || '';
        const textContent = codeElement.text();

        // Check if this is an HTMLJS code block by content
        if (className.toLowerCase().includes('htmljs') ||
            textContent.startsWith('```HTMLJS') ||
            textContent.startsWith('```htmljs') ||
            (textContent.includes('<div') && textContent.includes('style=')) ||
            (textContent.includes('<script') && textContent.includes('function')) ||
            (textContent.includes('<button') && textContent.includes('onclick'))) {
            console.log('Found potential HTMLJS block by content analysis:', className, textContent.substring(0, 50));
            codeBlocks = codeBlocks.add(codeElement);
        }
    });

    console.log(`Found ${codeBlocks.length} total HTMLJS code blocks to process`);

    // Debug: Log all code blocks found
    codeBlocks.each(function(index) {
        const codeBlock = $(this);
        console.log(`Code block ${index}:`, {
            className: codeBlock.attr('class'),
            textLength: codeBlock.text().length,
            textPreview: codeBlock.text().substring(0, 100)
        });
    });

    codeBlocks.each(function() {
        const codeBlock = $(this);
        const preElement = codeBlock.closest('pre');

        // Skip if already processed
        if (codeBlock.hasClass('htmljs-processed')) {
            return;
        }

        // Mark as processed
        codeBlock.addClass('htmljs-processed');

        // Get the raw text content
        let rawContent = codeBlock.text().trim();

        // Extract HTML content from HTMLJS code block syntax
        let htmlContent = rawContent;

        // If the content starts with ```HTMLJS, extract the inner content
        if (rawContent.startsWith('```HTMLJS') || rawContent.startsWith('```htmljs')) {
            const lines = rawContent.split('\n');
            // Remove first line (```HTMLJS) and last line (```)
            if (lines.length >= 3 && lines[lines.length - 1].trim() === '```') {
                htmlContent = lines.slice(1, -1).join('\n').trim();
            } else {
                // Handle case where ``` is on the same line or missing
                htmlContent = rawContent.replace(/^```(?:HTMLJS|htmljs)\s*/, '').replace(/```\s*$/, '').trim();
            }
        }

        console.log('Processing HTMLJS code block:');
        console.log('Raw content:', rawContent.substring(0, 100) + '...');
        console.log('Extracted HTML:', htmlContent.substring(0, 100) + '...');

        if (htmlContent.length <= extension_settings.htmlEmbed.maxTemplateSize) {
            // Validate HTML content for security
            if (isValidHtmlContent(htmlContent)) {
                const iframe = createSandboxedIframe(htmlContent, `codeblock_${Date.now()}`);
                preElement.replaceWith(iframe);
                console.log('Successfully replaced code block with iframe');
            } else {
                const errorHtml = `<div class="html-embed-error">❌ HTML content contains potentially dangerous elements</div>`;
                preElement.replaceWith(errorHtml);
                console.log('Replaced code block with security error');
            }
        } else {
            const errorHtml = `<div class="html-embed-error">❌ HTML content too large (max ${extension_settings.htmlEmbed.maxTemplateSize} bytes)</div>`;
            preElement.replaceWith(errorHtml);
            console.log('Replaced code block with size error');
        }
    });
}

// jQuery ready function to ensure proper initialization
jQuery(async function() {
    console.log('HTML Embed Extension: jQuery ready, initializing...');
    await init();

    // Add settings UI after extensions are loaded
    eventSource.on(event_types.EXTENSIONS_FIRST_LOAD, addSettingsUI);

    // Also try to add settings UI immediately in case the event already fired
    setTimeout(addSettingsUI, 2000);

    // Start global processors
    setInterval(processAllHtmljsPlaceholders, 2000);
    setInterval(processHtmljsCodeBlocks, 1000); // More frequent for code blocks

    console.log('HTML Embed Extension: Initialization complete');
});

/**
 * Store HTML template on server
 */
async function storeTemplate(html, name, description) {
    try {
        const response = await fetch('/api/plugins/html-embed/store-template', {
            method: 'POST',
            headers: {
                ...getRequestHeaders(),
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ html, name, description })
        });

        const data = await response.json();
        if (data.success) {
            templateCache.clear(); // Clear cache to force refresh
            return data.templateId;
        } else {
            throw new Error(data.error || 'Failed to store template');
        }
    } catch (error) {
        console.error('Failed to store template:', error);
        throw error;
    }
}

/**
 * List all templates
 */
async function listTemplates() {
    try {
        const response = await fetch('/api/plugins/html-embed/list-templates', {
            method: 'GET',
            headers: getRequestHeaders()
        });

        const data = await response.json();
        if (data.success) {
            return data.templates;
        } else {
            throw new Error(data.error || 'Failed to list templates');
        }
    } catch (error) {
        console.error('Failed to list templates:', error);
        return [];
    }
}

/**
 * Delete template
 */
async function deleteTemplate(templateId) {
    try {
        const response = await fetch(`/api/plugins/html-embed/delete-template/${templateId}`, {
            method: 'DELETE',
            headers: getRequestHeaders()
        });

        const data = await response.json();
        if (data.success) {
            templateCache.delete(templateId);
            return true;
        } else {
            throw new Error(data.error || 'Failed to delete template');
        }
    } catch (error) {
        console.error('Failed to delete template:', error);
        return false;
    }
}

/**
 * Show template creation dialog
 */
async function showCreateTemplateDialog() {
    const html = `
        <div class="html-embed-create-dialog">
            <h3>Create HTML Template</h3>
            <div class="form-group">
                <label for="template-name">Template Name:</label>
                <input type="text" id="template-name" placeholder="My Interactive Widget" />
            </div>
            <div class="form-group">
                <label for="template-description">Description:</label>
                <input type="text" id="template-description" placeholder="A brief description of what this template does" />
            </div>
            <div class="form-group">
                <label for="template-html">HTML Content:</label>
                <textarea id="template-html" rows="15" placeholder="Enter your HTML content here...">${getExampleTemplate()}</textarea>
            </div>
            <div class="form-group">
                <button type="button" id="preview-template">Preview</button>
                <button type="button" id="save-template">Save Template</button>
            </div>
            <div id="template-preview" style="display: none;">
                <h4>Preview:</h4>
                <div class="preview-container"></div>
            </div>
        </div>
    `;

    const popup = await callGenericPopup(html, POPUP_TYPE.TEXT, '', {
        wide: true,
        large: true,
        allowVerticalScrolling: true
    });

    // Handle preview
    $('#preview-template').on('click', function() {
        const htmlContent = $('#template-html').val();
        const previewContainer = $('#template-preview .preview-container');
        const iframe = createSandboxedIframe(htmlContent, 'preview');
        previewContainer.empty().append(iframe);
        $('#template-preview').show();
    });

    // Handle save
    $('#save-template').on('click', async function() {
        const name = $('#template-name').val().trim();
        const description = $('#template-description').val().trim();
        const html = $('#template-html').val().trim();

        if (!name || !html) {
            alert('Please provide both a name and HTML content.');
            return;
        }

        try {
            const templateId = await storeTemplate(html, name, description);
            alert(`Template saved successfully! ID: ${templateId}\n\nUse [html:${templateId}] in your messages to embed this template.`);
            $('#popup_text').empty();
        } catch (error) {
            alert(`Failed to save template: ${error.message}`);
        }
    });
}

/**
 * Show template management dialog
 */
async function showTemplateManagerDialog() {
    const templates = await listTemplates();

    let templateListHtml = '';
    if (templates.length === 0) {
        templateListHtml = '<p>No templates found. Create your first template!</p>';
    } else {
        templateListHtml = templates.map(template => `
            <div class="template-item" data-id="${template.id}">
                <h4>${template.name}</h4>
                <p>${template.description || 'No description'}</p>
                <div class="template-meta">
                    <small>ID: ${template.id}</small>
                    <small>Created: ${new Date(template.created).toLocaleDateString()}</small>
                </div>
                <div class="template-actions">
                    <button type="button" class="copy-id-btn" data-id="${template.id}">Copy ID</button>
                    <button type="button" class="edit-template-btn" data-id="${template.id}">Edit</button>
                    <button type="button" class="delete-template-btn" data-id="${template.id}">Delete</button>
                </div>
            </div>
        `).join('');
    }

    const html = `
        <div class="html-embed-manager-dialog">
            <h3>Template Manager</h3>
            <div class="template-list">
                ${templateListHtml}
            </div>
            <div class="manager-actions">
                <button type="button" id="create-new-template">Create New Template</button>
                <button type="button" id="refresh-templates">Refresh</button>
            </div>
        </div>
    `;

    await callGenericPopup(html, POPUP_TYPE.TEXT, '', {
        wide: true,
        large: true,
        allowVerticalScrolling: true
    });

    // Handle copy ID
    $('.copy-id-btn').on('click', function() {
        const templateId = $(this).data('id');
        navigator.clipboard.writeText(`[html:${templateId}]`).then(() => {
            alert('Template markup copied to clipboard!');
        });
    });

    // Handle delete
    $('.delete-template-btn').on('click', async function() {
        const templateId = $(this).data('id');
        if (confirm('Are you sure you want to delete this template?')) {
            const success = await deleteTemplate(templateId);
            if (success) {
                $(this).closest('.template-item').remove();
                alert('Template deleted successfully!');
            } else {
                alert('Failed to delete template.');
            }
        }
    });

    // Handle create new
    $('#create-new-template').on('click', function() {
        $('#popup_text').empty();
        showCreateTemplateDialog();
    });

    // Handle refresh
    $('#refresh-templates').on('click', function() {
        $('#popup_text').empty();
        showTemplateManagerDialog();
    });
}

/**
 * Get example template
 */
function getExampleTemplate() {
    return `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        .toggle-container {
            text-align: center;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .toggle-button {
            background: linear-gradient(45deg, #2196F3, #21CBF3);
            border: none;
            color: white;
            padding: 15px 30px;
            font-size: 16px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }
        .toggle-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(33, 150, 243, 0.4);
        }
        .status {
            margin: 20px 0;
            font-size: 18px;
            font-weight: bold;
        }
        .status.on { color: #4CAF50; }
        .status.off { color: #f44336; }
    </style>
</head>
<body>
    <div class="toggle-container">
        <h3>Interactive Toggle Example</h3>
        <div class="status off" id="status">Status: OFF</div>
        <button class="toggle-button" onclick="toggleStatus()">Toggle</button>
    </div>

    <script>
        let isOn = false;

        function toggleStatus() {
            isOn = !isOn;
            const statusEl = document.getElementById('status');

            if (isOn) {
                statusEl.textContent = 'Status: ON';
                statusEl.className = 'status on';
            } else {
                statusEl.textContent = 'Status: OFF';
                statusEl.className = 'status off';
            }
        }
    </script>
</body>
</html>`;
}

/**
 * Load settings
 */
function loadSettings() {
    $('#html_embed_enabled').prop('checked', extension_settings.htmlEmbed.enabled);
    $('#html_embed_sandbox_mode').prop('checked', extension_settings.htmlEmbed.sandboxMode);
    $('#html_embed_template_system').prop('checked', extension_settings.htmlEmbed.enableTemplateSystem);
    $('#html_embed_inline_html').prop('checked', extension_settings.htmlEmbed.enableInlineHtml);
    $('#html_embed_max_size').val(extension_settings.htmlEmbed.maxTemplateSize);
    $('#html_embed_strict_security').prop('checked', extension_settings.htmlEmbed.strictSecurity);
    $('#html_embed_allow_external_resources').prop('checked', extension_settings.htmlEmbed.allowExternalResources);
    $('#html_embed_allow_dangerous_functions').prop('checked', extension_settings.htmlEmbed.allowDangerousFunctions);
}

/**
 * Save settings
 */
function saveSettings() {
    extension_settings.htmlEmbed.enabled = $('#html_embed_enabled').prop('checked');
    extension_settings.htmlEmbed.sandboxMode = $('#html_embed_sandbox_mode').prop('checked');
    extension_settings.htmlEmbed.enableTemplateSystem = $('#html_embed_template_system').prop('checked');
    extension_settings.htmlEmbed.enableInlineHtml = $('#html_embed_inline_html').prop('checked');
    extension_settings.htmlEmbed.maxTemplateSize = parseInt($('#html_embed_max_size').val()) || defaultSettings.maxTemplateSize;
    extension_settings.htmlEmbed.strictSecurity = $('#html_embed_strict_security').prop('checked');
    extension_settings.htmlEmbed.allowExternalResources = $('#html_embed_allow_external_resources').prop('checked');
    extension_settings.htmlEmbed.allowDangerousFunctions = $('#html_embed_allow_dangerous_functions').prop('checked');

    saveSettingsDebounced();
}

/**
 * Add settings UI
 */
async function addSettingsUI() {
    try {
        console.log('Adding HTML Embed settings UI...');

        // Try multiple possible container selectors
        const possibleContainers = [
            '#html_embed_container',
            '#extensions_settings',
            '#extensions_settings2',
            '.extensions_block',
            '#extensions'
        ];

        let container = null;
        for (const selector of possibleContainers) {
            const element = $(selector);
            if (element.length > 0) {
                container = element;
                console.log(`HTML Embed: Found container with selector: ${selector}`);
                break;
            }
        }

        if (!container) {
            console.warn('HTML Embed: No suitable container found, retrying in 2 seconds...');
            setTimeout(addSettingsUI, 2000);
            return;
        }

        const settingsHtml = await renderExtensionTemplateAsync('html-embed', 'settings');

        // Create a dedicated section for HTML Embed
        const htmlEmbedSection = `
            <div class="extension_block" id="html_embed_extension_block">
                <h3>HTML Embed Extension</h3>
                ${settingsHtml}
            </div>
        `;

        container.append(htmlEmbedSection);

        // Bind event handlers
        $('#html_embed_enabled, #html_embed_sandbox_mode, #html_embed_template_system, #html_embed_inline_html, #html_embed_strict_security, #html_embed_allow_external_resources, #html_embed_allow_dangerous_functions').on('change', saveSettings);
        $('#html_embed_max_size').on('input', saveSettings);

        $('#html_embed_create_template').on('click', showCreateTemplateDialog);
        $('#html_embed_manage_templates').on('click', showTemplateManagerDialog);

        loadSettings();

        console.log('HTML Embed settings UI added successfully');
    } catch (error) {
        console.error('Failed to add HTML Embed settings UI:', error);
        // Retry after error
        setTimeout(addSettingsUI, 3000);
    }
}

// Remove the old initialization code since we're using jQuery ready function above
