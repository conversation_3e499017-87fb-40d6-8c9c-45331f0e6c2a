<div class="html-embed-settings">
    <div class="inline-drawer">
        <div class="inline-drawer-toggle inline-drawer-header">
            <b>HTML Embed Extension</b>
            <div class="inline-drawer-icon fa-solid fa-circle-chevron-down down"></div>
        </div>
        <div class="inline-drawer-content">
            <div class="html-embed-setting-group">
                <label class="checkbox_label">
                    <input type="checkbox" id="html_embed_enabled" />
                    <span>Enable HTML Embed Extension</span>
                </label>
                <small>Allow embedding interactive HTML/JS content in chat messages</small>
            </div>
    
    <div class="html-embed-setting-group">
        <label class="checkbox_label">
            <input type="checkbox" id="html_embed_sandbox_mode" />
            <span>Sandbox Mode (Recommended)</span>
        </label>
        <small>Run HTML content in isolated iframes for security</small>
    </div>
    
    <div class="html-embed-setting-group">
        <label class="checkbox_label">
            <input type="checkbox" id="html_embed_template_system" />
            <span>Enable Template System</span>
        </label>
        <small>Allow using stored HTML templates with [html:template_id] syntax</small>
    </div>
    
    <div class="html-embed-setting-group">
        <label class="checkbox_label">
            <input type="checkbox" id="html_embed_inline_html" />
            <span>Enable Inline HTML (Advanced)</span>
        </label>
        <small>Allow inline HTML with [html-inline:...] syntax. Use with caution!</small>
    </div>
    
    <div class="html-embed-setting-group">
        <label for="html_embed_max_size">Maximum Template Size (bytes):</label>
        <input type="number" id="html_embed_max_size" min="1000" max="1000000" step="1000" />
        <small>Maximum size for HTML templates and inline content</small>
    </div>
    
    <div class="html-embed-setting-group">
        <h4>Template Management</h4>
        <div class="html-embed-template-controls">
            <button type="button" id="html_embed_create_template" class="menu_button">
                <i class="fa fa-plus"></i> Create New Template
            </button>
            <button type="button" id="html_embed_manage_templates" class="menu_button">
                <i class="fa fa-list"></i> Manage Templates
            </button>
            <button type="button" id="html_embed_import_template" class="menu_button">
                <i class="fa fa-upload"></i> Import Template
            </button>
        </div>
    </div>
    
    <div class="html-embed-setting-group">
        <h4>Usage Examples</h4>
        <div class="html-embed-examples">
            <div class="example-item">
                <strong>Code Block Syntax (Recommended):</strong>
                <code>```HTMLJS<br>&lt;button onclick="alert('Hello!')"&gt;Click me&lt;/button&gt;<br>```</code>
                <small>Use code blocks with HTMLJS language tag - always enabled and most convenient</small>
            </div>
            <div class="example-item">
                <strong>Template Reference:</strong>
                <code>[html:a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6]</code>
                <small>References a stored HTML template by ID</small>
            </div>
            <div class="example-item">
                <strong>Inline HTML:</strong>
                <code>[html-inline:&lt;button onclick="alert('Hello!')"&gt;Click me&lt;/button&gt;]</code>
                <small>Embeds HTML directly (if enabled)</small>
            </div>
        </div>
    </div>
    
    <div class="html-embed-setting-group">
        <h4>Security Settings</h4>

        <label>
            <input type="checkbox" id="html_embed_strict_security" />
            Enable Strict Security Validation
            <small>Validates HTML content for potentially dangerous elements before embedding</small>
        </label>

        <label>
            <input type="checkbox" id="html_embed_allow_external_resources" />
            Allow External Resources
            <small>⚠️ Allows external scripts, stylesheets, iframes, etc. (Use with caution!)</small>
        </label>

        <label>
            <input type="checkbox" id="html_embed_allow_dangerous_functions" />
            Allow Dangerous Functions
            <small>⚠️ Allows eval(), Function() and other potentially dangerous JavaScript (Use with extreme caution!)</small>
        </label>
    </div>

    <div class="html-embed-setting-group">
        <h4>Security Notice</h4>
        <div class="html-embed-security-notice">
            <p><strong>⚠️ Important Security Information:</strong></p>
            <ul>
                <li>HTML content runs in sandboxed iframes by default</li>
                <li>Strict security validation blocks external resources and dangerous functions</li>
                <li>Only disable security features if you trust all chat participants</li>
                <li>Templates are stored on the server and can be managed centrally</li>
                <li>Always review HTML content before embedding</li>
                <li><strong>Disabling security features may expose you to XSS attacks!</strong></li>
            </ul>
            </div>
        </div>
    </div>
</div>

<style>
.html-embed-settings {
    padding: 15px;
    max-width: 600px;
}

.html-embed-setting-group {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--SmartThemeBorderColor, #eee);
}

.html-embed-setting-group:last-child {
    border-bottom: none;
}

.html-embed-setting-group h3 {
    margin-top: 0;
    color: var(--SmartThemeQuoteColor, #333);
}

.html-embed-setting-group h4 {
    margin: 10px 0 8px 0;
    color: var(--SmartThemeQuoteColor, #555);
    font-size: 14px;
}

.html-embed-setting-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: var(--SmartThemeQuoteColor, #333);
}

.html-embed-setting-group input[type="number"] {
    width: 150px;
    padding: 6px 10px;
    border: 1px solid var(--SmartThemeBorderColor, #ccc);
    border-radius: 4px;
    background: var(--SmartThemeBodyColor, white);
    color: var(--SmartThemeQuoteColor, #333);
}

.html-embed-setting-group small {
    display: block;
    margin-top: 5px;
    color: var(--SmartThemeQuoteColor, #666);
    font-size: 12px;
    line-height: 1.4;
}

.html-embed-template-controls {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    margin-top: 10px;
}

.html-embed-template-controls button {
    padding: 8px 12px;
    font-size: 13px;
}

.html-embed-examples {
    background: var(--SmartThemeBlurTintColor, #f8f9fa);
    padding: 15px;
    border-radius: 6px;
    border: 1px solid var(--SmartThemeBorderColor, #dee2e6);
}

.example-item {
    margin-bottom: 15px;
}

.example-item:last-child {
    margin-bottom: 0;
}

.example-item strong {
    display: block;
    margin-bottom: 5px;
    color: var(--SmartThemeQuoteColor, #333);
}

.example-item code {
    display: block;
    background: var(--SmartThemeBodyColor, white);
    padding: 8px;
    border-radius: 4px;
    border: 1px solid var(--SmartThemeBorderColor, #ccc);
    font-family: 'Courier New', monospace;
    font-size: 12px;
    margin: 5px 0;
    word-break: break-all;
}

.example-item small {
    margin-top: 5px;
}

.html-embed-security-notice {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    padding: 15px;
    color: #856404;
}

.html-embed-security-notice p {
    margin: 0 0 10px 0;
    font-weight: 600;
}

.html-embed-security-notice ul {
    margin: 0;
    padding-left: 20px;
}

.html-embed-security-notice li {
    margin-bottom: 5px;
    line-height: 1.4;
}

@media (max-width: 768px) {
    .html-embed-template-controls {
        flex-direction: column;
    }
    
    .html-embed-template-controls button {
        width: 100%;
    }
}
</style>
