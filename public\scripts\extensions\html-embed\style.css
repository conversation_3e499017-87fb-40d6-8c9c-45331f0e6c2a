/* HTML Embed Extension Styles */

.html-embed-iframe {
    width: 100%;
    min-height: 200px;
    border: 1px solid var(--SmartThemeBorderColor, #ccc);
    border-radius: 8px;
    background: var(--SmartThemeBodyColor, white);
    margin: 10px 0;
    resize: vertical;
    overflow: hidden;
    transition: height 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.html-embed-error {
    background: #ffebee;
    color: #c62828;
    padding: 12px;
    border-radius: 6px;
    border-left: 4px solid #c62828;
    margin: 10px 0;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.4;
}

.html-embed-controls {
    margin: 10px 0;
    padding: 12px;
    background: var(--SmartThemeBlurTintColor, #f8f9fa);
    border-radius: 6px;
    border: 1px solid var(--SmartThemeBorderColor, #dee2e6);
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.html-embed-controls button {
    background: var(--SmartThemeBlurTintColor, #007bff);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
}

.html-embed-controls button:hover {
    opacity: 0.8;
    transform: translateY(-1px);
}

.html-embed-controls button:active {
    transform: translateY(0);
}

.html-embed-controls .template-info {
    font-size: 12px;
    color: var(--SmartThemeQuoteColor, #666);
    margin-left: auto;
}

.html-embed-template-manager {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: var(--SmartThemeBodyColor, white);
    border: 1px solid var(--SmartThemeBorderColor, #ccc);
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    z-index: 9999;
    max-width: 80vw;
    max-height: 80vh;
    overflow: auto;
    padding: 20px;
}

.html-embed-template-manager h3 {
    margin-top: 0;
    color: var(--SmartThemeQuoteColor, #333);
    border-bottom: 1px solid var(--SmartThemeBorderColor, #eee);
    padding-bottom: 10px;
}

.html-embed-template-list {
    max-height: 300px;
    overflow-y: auto;
    margin: 15px 0;
}

.html-embed-template-item {
    padding: 10px;
    border: 1px solid var(--SmartThemeBorderColor, #eee);
    border-radius: 4px;
    margin-bottom: 10px;
    background: var(--SmartThemeBlurTintColor, #f8f9fa);
}

.html-embed-template-item h4 {
    margin: 0 0 5px 0;
    color: var(--SmartThemeQuoteColor, #333);
}

.html-embed-template-item p {
    margin: 0 0 10px 0;
    font-size: 12px;
    color: var(--SmartThemeQuoteColor, #666);
}

.html-embed-template-item .template-actions {
    display: flex;
    gap: 5px;
}

.html-embed-template-item button {
    padding: 4px 8px;
    font-size: 11px;
    border-radius: 3px;
}

.html-embed-editor {
    margin: 15px 0;
}

.html-embed-editor textarea {
    width: 100%;
    height: 200px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    padding: 10px;
    border: 1px solid var(--SmartThemeBorderColor, #ccc);
    border-radius: 4px;
    background: var(--SmartThemeBodyColor, white);
    color: var(--SmartThemeQuoteColor, #333);
    resize: vertical;
}

.html-embed-editor input[type="text"] {
    width: 100%;
    padding: 8px;
    margin: 5px 0;
    border: 1px solid var(--SmartThemeBorderColor, #ccc);
    border-radius: 4px;
    background: var(--SmartThemeBodyColor, white);
    color: var(--SmartThemeQuoteColor, #333);
}

.html-embed-preview {
    border: 1px solid var(--SmartThemeBorderColor, #ccc);
    border-radius: 4px;
    padding: 10px;
    margin: 10px 0;
    background: var(--SmartThemeBodyColor, white);
    min-height: 100px;
}

.html-embed-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 9998;
}

/* Responsive design */
@media (max-width: 768px) {
    .html-embed-template-manager {
        max-width: 95vw;
        max-height: 90vh;
        padding: 15px;
    }
    
    .html-embed-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .html-embed-controls .template-info {
        margin-left: 0;
        margin-top: 5px;
    }
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
    .html-embed-error {
        background: #3d1a1a;
        color: #ff6b6b;
        border-left-color: #ff6b6b;
    }
}

/* Animation for iframe loading */
.html-embed-iframe.loading {
    opacity: 0.7;
    pointer-events: none;
}

.html-embed-iframe.loaded {
    opacity: 1;
    pointer-events: auto;
}

/* Scrollbar styling for template list */
.html-embed-template-list::-webkit-scrollbar {
    width: 8px;
}

.html-embed-template-list::-webkit-scrollbar-track {
    background: var(--SmartThemeBlurTintColor, #f1f1f1);
    border-radius: 4px;
}

.html-embed-template-list::-webkit-scrollbar-thumb {
    background: var(--SmartThemeBorderColor, #c1c1c1);
    border-radius: 4px;
}

.html-embed-template-list::-webkit-scrollbar-thumb:hover {
    background: var(--SmartThemeQuoteColor, #a8a8a8);
}
