// Interactive Chat Widget Extension for SillyTavern
import { extension_settings, getContext, loadExtensionSettings } from "../../extensions.js";
import { saveSettingsDebounced, eventSource, event_types, chat } from "../../../script.js";
import { renderExtensionTemplateAsync } from "../../extensions.js";

const extensionName = "interactive-chat";
const extensionFolderPath = `scripts/extensions/${extensionName}`;

// Default settings
const defaultSettings = {
    enabled: true,
    allowInMessages: true,
    widgetStyle: 'modern'
};

// Widget state management
let widgetInstances = new Map();
let widgetCounter = 0;

// Initialize extension settings
async function loadSettings() {
    extension_settings[extensionName] = extension_settings[extensionName] || {};
    if (Object.keys(extension_settings[extensionName]).length === 0) {
        Object.assign(extension_settings[extensionName], defaultSettings);
    }
    
    // Update UI elements if they exist
    $("#interactive_chat_enabled").prop("checked", extension_settings[extensionName].enabled).trigger("input");
    $("#interactive_chat_allow_messages").prop("checked", extension_settings[extensionName].allowInMessages).trigger("input");
}

// Settings change handlers
function onEnabledChange(event) {
    const value = Boolean($(event.target).prop("checked"));
    extension_settings[extensionName].enabled = value;
    saveSettingsDebounced();
}

function onAllowMessagesChange(event) {
    const value = Boolean($(event.target).prop("checked"));
    extension_settings[extensionName].allowInMessages = value;
    saveSettingsDebounced();
}

// Create interactive widget HTML
function createInteractiveWidget(type = 'toggle', options = {}) {
    const widgetId = `widget_${++widgetCounter}`;
    const defaultOptions = {
        title: '互動控制面板',
        initialState: false,
        showCounter: false,
        showProgress: false,
        counterValue: 0,
        progressValue: 0
    };
    
    const config = { ...defaultOptions, ...options };
    
    let widgetHTML = '';
    
    switch (type) {
        case 'toggle':
            widgetHTML = `
                <div class="interactive-widget" data-widget-id="${widgetId}">
                    <div class="widget-container">
                        <h3>${config.title}</h3>
                        <div class="widget-display-box widget-state-${config.initialState ? 'on' : 'off'}" id="${widgetId}_display">
                            ${config.initialState ? '開啟' : '關閉'}
                        </div>
                        <div class="widget-status-text" id="${widgetId}_status">
                            當前狀態：${config.initialState ? '開啟' : '關閉'}
                        </div>
                        <button class="widget-toggle-btn" onclick="window.toggleWidget('${widgetId}')">
                            切換狀態
                        </button>
                        <div class="widget-switch ${config.initialState ? 'active' : ''}" id="${widgetId}_switch" onclick="window.toggleWidget('${widgetId}')">
                            <div class="widget-switch-knob"></div>
                        </div>
                    </div>
                </div>
            `;
            break;
            
        case 'counter':
            widgetHTML = `
                <div class="interactive-widget" data-widget-id="${widgetId}">
                    <div class="widget-container">
                        <h3>${config.title}</h3>
                        <div class="widget-counter" id="${widgetId}_counter">${config.counterValue}</div>
                        <div class="widget-input-group">
                            <button class="widget-small-btn" onclick="window.adjustCounter('${widgetId}', -1)">-1</button>
                            <button class="widget-small-btn" onclick="window.adjustCounter('${widgetId}', -10)">-10</button>
                            <button class="widget-small-btn" onclick="window.resetCounter('${widgetId}')">重置</button>
                            <button class="widget-small-btn" onclick="window.adjustCounter('${widgetId}', 10)">+10</button>
                            <button class="widget-small-btn" onclick="window.adjustCounter('${widgetId}', 1)">+1</button>
                        </div>
                    </div>
                </div>
            `;
            break;
            
        case 'progress':
            widgetHTML = `
                <div class="interactive-widget" data-widget-id="${widgetId}">
                    <div class="widget-container">
                        <h3>${config.title}</h3>
                        <div class="widget-progress-bar">
                            <div class="widget-progress-fill" id="${widgetId}_progress" style="width: ${config.progressValue}%"></div>
                        </div>
                        <div class="widget-status-text" id="${widgetId}_progress_text">${config.progressValue}%</div>
                        <div class="widget-input-group">
                            <button class="widget-small-btn" onclick="window.adjustProgress('${widgetId}', -10)">-10%</button>
                            <button class="widget-small-btn" onclick="window.adjustProgress('${widgetId}', -25)">-25%</button>
                            <button class="widget-small-btn" onclick="window.resetProgress('${widgetId}')">重置</button>
                            <button class="widget-small-btn" onclick="window.adjustProgress('${widgetId}', 25)">+25%</button>
                            <button class="widget-small-btn" onclick="window.adjustProgress('${widgetId}', 10)">+10%</button>
                        </div>
                    </div>
                </div>
            `;
            break;
            
        case 'input':
            widgetHTML = `
                <div class="interactive-widget" data-widget-id="${widgetId}">
                    <div class="widget-container">
                        <h3>${config.title}</h3>
                        <div class="widget-input-group">
                            <input type="text" class="widget-input" id="${widgetId}_input" placeholder="輸入內容..." />
                            <button class="widget-toggle-btn" onclick="window.processInput('${widgetId}')">提交</button>
                        </div>
                        <div class="widget-status-text" id="${widgetId}_output">等待輸入...</div>
                    </div>
                </div>
            `;
            break;
    }
    
    // Store widget state
    widgetInstances.set(widgetId, {
        type: type,
        state: config.initialState,
        counter: config.counterValue,
        progress: config.progressValue,
        config: config
    });
    
    return widgetHTML;
}

// Widget interaction functions (attached to window for global access)
window.toggleWidget = function(widgetId) {
    const widget = widgetInstances.get(widgetId);
    if (!widget) return;
    
    widget.state = !widget.state;
    
    const displayBox = document.getElementById(`${widgetId}_display`);
    const statusText = document.getElementById(`${widgetId}_status`);
    const switchElement = document.getElementById(`${widgetId}_switch`);
    
    if (widget.state) {
        displayBox.className = 'widget-display-box widget-state-on';
        displayBox.textContent = '開啟';
        statusText.textContent = '當前狀態：開啟';
        switchElement.classList.add('active');
    } else {
        displayBox.className = 'widget-display-box widget-state-off';
        displayBox.textContent = '關閉';
        statusText.textContent = '當前狀態：關閉';
        switchElement.classList.remove('active');
    }
    
    widgetInstances.set(widgetId, widget);
};

window.adjustCounter = function(widgetId, amount) {
    const widget = widgetInstances.get(widgetId);
    if (!widget) return;
    
    widget.counter = Math.max(0, widget.counter + amount);
    
    const counterElement = document.getElementById(`${widgetId}_counter`);
    if (counterElement) {
        counterElement.textContent = widget.counter;
    }
    
    widgetInstances.set(widgetId, widget);
};

window.resetCounter = function(widgetId) {
    const widget = widgetInstances.get(widgetId);
    if (!widget) return;
    
    widget.counter = 0;
    
    const counterElement = document.getElementById(`${widgetId}_counter`);
    if (counterElement) {
        counterElement.textContent = widget.counter;
    }
    
    widgetInstances.set(widgetId, widget);
};

window.adjustProgress = function(widgetId, amount) {
    const widget = widgetInstances.get(widgetId);
    if (!widget) return;
    
    widget.progress = Math.max(0, Math.min(100, widget.progress + amount));
    
    const progressElement = document.getElementById(`${widgetId}_progress`);
    const textElement = document.getElementById(`${widgetId}_progress_text`);
    
    if (progressElement) {
        progressElement.style.width = `${widget.progress}%`;
    }
    if (textElement) {
        textElement.textContent = `${widget.progress}%`;
    }
    
    widgetInstances.set(widgetId, widget);
};

window.resetProgress = function(widgetId) {
    const widget = widgetInstances.get(widgetId);
    if (!widget) return;
    
    widget.progress = 0;
    
    const progressElement = document.getElementById(`${widgetId}_progress`);
    const textElement = document.getElementById(`${widgetId}_progress_text`);
    
    if (progressElement) {
        progressElement.style.width = '0%';
    }
    if (textElement) {
        textElement.textContent = '0%';
    }
    
    widgetInstances.set(widgetId, widget);
};

window.processInput = function(widgetId) {
    const inputElement = document.getElementById(`${widgetId}_input`);
    const outputElement = document.getElementById(`${widgetId}_output`);
    
    if (inputElement && outputElement) {
        const value = inputElement.value.trim();
        if (value) {
            outputElement.textContent = `您輸入了：${value}`;
            inputElement.value = '';
        } else {
            outputElement.textContent = '請輸入有效內容';
        }
    }
};

// Slash command to insert widgets
function registerSlashCommands() {
    // Register slash command for creating widgets
    window.SlashCommandParser?.addCommand('widget', (args, value) => {
        if (!extension_settings[extensionName].enabled) {
            return 'Interactive Chat Widget extension is disabled.';
        }
        
        const type = args.type || 'toggle';
        const title = args.title || '互動組件';
        const options = {
            title: title,
            initialState: args.state === 'true' || args.state === 'on',
            counterValue: parseInt(args.counter) || 0,
            progressValue: parseInt(args.progress) || 0
        };
        
        return createInteractiveWidget(type, options);
    }, [], '<span class="monospace">/widget type=toggle title="我的組件"</span> – 創建互動組件', true, true);
}

// Extension initialization
jQuery(async () => {
    // Load settings HTML
    const settingsHtml = `
        <div class="interactive-chat-settings">
            <h3>Interactive Chat Widget Settings</h3>
            <label class="checkbox_label" for="interactive_chat_enabled">
                <input id="interactive_chat_enabled" type="checkbox" />
                <span>Enable Interactive Chat Widgets</span>
            </label>
            <label class="checkbox_label" for="interactive_chat_allow_messages">
                <input id="interactive_chat_allow_messages" type="checkbox" />
                <span>Allow widgets in chat messages</span>
            </label>
        </div>
    `;
    
    // Append to extensions settings
    $("#extensions_settings2").append(settingsHtml);
    
    // Bind event handlers
    $("#interactive_chat_enabled").on("input", onEnabledChange);
    $("#interactive_chat_allow_messages").on("input", onAllowMessagesChange);
    
    // Load settings
    await loadSettings();
    
    // Register slash commands
    registerSlashCommands();
    
    console.log('Interactive Chat Widget extension loaded');
});
