/* Interactive Chat Widget Styles */
.interactive-widget {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    padding: 20px;
    margin: 10px 0;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    color: white;
    font-family: 'Arial', sans-serif;
}

.widget-container {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.18);
}

.widget-display-box {
    width: 150px;
    height: 150px;
    margin: 15px auto;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    font-weight: bold;
    color: white;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.widget-state-off {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
}

.widget-state-on {
    background: linear-gradient(45deg, #00d2d3, #54a0ff);
}

.widget-toggle-btn {
    background: linear-gradient(45deg, #2196F3, #21CBF3);
    border: none;
    color: white;
    padding: 12px 25px;
    font-size: 14px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 10px;
    box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
}

.widget-toggle-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(33, 150, 243, 0.4);
}

.widget-status-text {
    color: white;
    font-size: 16px;
    margin: 15px 0;
}

.widget-switch {
    position: relative;
    width: 50px;
    height: 25px;
    background: #ccc;
    border-radius: 12px;
    cursor: pointer;
    transition: background 0.3s;
    margin: 15px auto;
}

.widget-switch.active {
    background: #4CAF50;
}

.widget-switch-knob {
    position: absolute;
    top: 2px;
    left: 2px;
    width: 21px;
    height: 21px;
    background: white;
    border-radius: 50%;
    transition: transform 0.3s;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.widget-switch.active .widget-switch-knob {
    transform: translateX(25px);
}

.widget-counter {
    font-size: 24px;
    font-weight: bold;
    color: #FFD700;
    margin: 10px 0;
}

.widget-progress-bar {
    width: 100%;
    height: 10px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 5px;
    overflow: hidden;
    margin: 10px 0;
}

.widget-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #00d2d3, #54a0ff);
    transition: width 0.3s ease;
    border-radius: 5px;
}

.widget-input-group {
    margin: 15px 0;
}

.widget-input {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    padding: 8px 12px;
    color: white;
    font-size: 14px;
    width: 200px;
}

.widget-input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.widget-input:focus {
    outline: none;
    border-color: #54a0ff;
    box-shadow: 0 0 10px rgba(84, 160, 255, 0.3);
}

.widget-small-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 5px 10px;
    font-size: 12px;
    border-radius: 15px;
    cursor: pointer;
    margin: 0 5px;
    transition: all 0.2s ease;
}

.widget-small-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}
